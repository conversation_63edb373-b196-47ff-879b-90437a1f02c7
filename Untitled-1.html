<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Investment Platform</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts (Inter) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons CDN -->
    <script src="https://unpkg.com/lucide-icons"></script>

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0d1117;
            color: #e6edf3;
        }
        .glass-card {
            background: rgba(17, 24, 39, 0.8); /* bg-gray-900 with opacity */
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .btn-primary { @apply bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors; }
        .btn-secondary { @apply bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition-colors; }
        .btn-accent { @apply bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-bold py-2 px-4 rounded-lg transition-colors; }
        .sidebar-link { @apply flex items-center p-3 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors; }
        .sidebar-link.active { @apply bg-blue-600 text-white; }
        .form-input { @apply w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500; }
        [x-cloak] { display: none !important; }
        .tab-button { @apply px-4 py-2 font-semibold text-gray-400 border-b-2 border-transparent hover:text-white hover:border-blue-500 transition; }
        .tab-button.active { @apply text-blue-500 border-blue-500; }
    </style>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'dark-bg': '#0d1117',
                        'primary': '#3b82f6',
                        'accent': '#f59e0b',
                    }
                }
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-dark-bg text-gray-300" x-data="appState()" x-init="init()">

    <!-- OTP Modal -->
    <div x-show="showOtpModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50" x-cloak>
        <div class="glass-card rounded-lg p-8 w-full max-w-sm">
            <h2 class="text-2xl font-bold text-center text-white mb-4">OTP Verification</h2>
            <p class="text-center text-gray-400 mb-6">Enter the OTP sent to your email.</p>
            <div class="flex justify-center space-x-2 mb-6">
                <input type="text" maxlength="1" class="w-12 h-12 text-center text-2xl font-bold bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500">
                <input type="text" maxlength="1" class="w-12 h-12 text-center text-2xl font-bold bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500">
                <input type="text" maxlength="1" class="w-12 h-12 text-center text-2xl font-bold bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500">
                <input type="text" maxlength="1" class="w-12 h-12 text-center text-2xl font-bold bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <button @click="showOtpModal = false" class="w-full btn-primary">Verify OTP</button>
        </div>
    </div>

    <!-- Main Container -->
    <div id="app">

        <!-- Home Page -->
        <div id="home-page" x-show="page === 'home'" x-cloak>
            <header class="bg-gray-900/80 backdrop-blur-sm sticky top-0 z-40">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <div class="text-2xl font-bold text-white">Crypto<span class="text-blue-500">Platform</span></div>
                    <div class="hidden lg:flex items-center space-x-6">
                        <a href="#about" class="text-gray-300 hover:text-blue-400">About</a>
                        <a href="#aibot" class="text-gray-300 hover:text-blue-400">AI Bot</a>
                        <a href="#faq" class="text-gray-300 hover:text-blue-400">FAQ</a>
                        <a href="#reviews" class="text-gray-300 hover:text-blue-400">User Review</a>
                        <a href="#community" class="text-gray-300 hover:text-blue-400">Our Community</a>
                        <a href="#contact" class="text-gray-300 hover:text-blue-400">Contact Us</a>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button @click="navigateTo('login')" class="btn-secondary">Login</button>
                        <button @click="navigateTo('register')" class="btn-primary">Register</button>
                    </div>
                </nav>
            </header>
            <main>
                <!-- Account Types Section -->
                <section id="accounts" class="py-16 bg-gray-900">
                    <div class="container mx-auto px-6">
                        <h2 class="text-3xl font-bold text-center mb-12 text-white">Choose Your Account Type</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="home-accounts-grid">
                            <!-- JS will populate this -->
                        </div>
                    </div>
                </section>
                <!-- Other home page sections -->
            </main>
        </div>

        <!-- Register Page -->
        <div id="register-page" x-show="page === 'register'" class="min-h-screen flex items-center justify-center bg-dark-bg p-4" x-cloak>
            <div class="w-full max-w-md">
                <div class="glass-card rounded-xl p-8">
                    <h2 class="text-3xl font-bold text-center text-white mb-6">Create Account</h2>
                    <form class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-400">Refer ID</label>
                            <input type="text" class="form-input mt-1" value="REF12345" disabled>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400">Refer Name</label>
                            <input type="text" class="form-input mt-1" value="John Doe" disabled>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400">User Name</label>
                            <input type="text" class="form-input mt-1" placeholder="Choose a username">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400">Email ID</label>
                            <input type="email" class="form-input mt-1" placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400">Mobile</label>
                            <input type="tel" class="form-input mt-1" placeholder="****** 456 7890">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400">Create Password</label>
                            <input type="password" class="form-input mt-1">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400">Confirm Password</label>
                            <input type="password" class="form-input mt-1">
                        </div>
                        <div class="flex items-center">
                            <input id="terms" type="checkbox" class="h-4 w-4 bg-gray-700 border-gray-600 text-blue-600 focus:ring-blue-500 rounded">
                            <label for="terms" class="ml-2 block text-sm text-gray-400">I accept all terms and conditions, Disclaimer</label>
                        </div>
                        <button type="submit" @click.prevent="navigateTo('dashboard')" class="w-full btn-primary !mt-6">Submit</button>
                    </form>
                    <p class="text-center text-gray-400 mt-6">
                        Already have an account? <a href="#" @click.prevent="navigateTo('login')" class="font-medium text-blue-500 hover:text-blue-400">Login</a>
                    </p>
                </div>
            </div>
        </div>

        <!-- Login Page -->
        <div id="login-page" x-show="page === 'login'" class="min-h-screen flex items-center justify-center bg-dark-bg p-4" x-cloak>
            <div class="w-full max-w-md">
                <div class="glass-card rounded-xl p-8">
                    <h2 class="text-3xl font-bold text-center text-white mb-6">Welcome Back</h2>
                    <form class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-400">User ID / Email</label>
                            <input type="text" class="form-input mt-1" placeholder="Enter your ID or email">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400">Password</label>
                            <input type="password" class="form-input mt-1" placeholder="Enter your password">
                        </div>
                        <div class="text-center text-sm text-yellow-400 bg-yellow-900/50 border border-yellow-700 rounded-lg p-3">
                            Email OTP is mandatory for security reasons.
                        </div>
                        <button type="submit" @click.prevent="navigateTo('dashboard')" class="w-full btn-primary !mt-6">Login</button>
                    </form>
                    <p class="text-center text-gray-400 mt-6">
                        Don't have an account? <a href="#" @click.prevent="navigateTo('register')" class="font-medium text-blue-500 hover:text-blue-400">Register</a>
                    </p>
                </div>
            </div>
        </div>

        <!-- Dashboard Page -->
        <div id="dashboard-page" x-show="page === 'dashboard'" class="flex h-screen bg-dark-bg" x-cloak>
            <!-- Sidebar -->
            <aside class="w-64 bg-gray-900 p-4 space-y-2 flex-shrink-0">
                <div class="text-2xl font-bold text-white mb-6">Crypto<span class="text-blue-500">Platform</span></div>
                <a href="#" @click.prevent="dashboardView = 'dashboard'" :class="{'active': dashboardView === 'dashboard'}" class="sidebar-link"><i data-lucide="layout-dashboard" class="mr-3"></i> Dashboard</a>
                
                <div x-data="{ open: false }">
                    <a href="#" @click.prevent="open = !open" class="sidebar-link justify-between">
                        <span class="flex items-center"><i data-lucide="user" class="mr-3"></i> My Account</span>
                        <i data-lucide="chevron-down" class="transition-transform" :class="{'rotate-180': open}"></i>
                    </a>
                    <div x-show="open" class="pl-6 space-y-1 mt-1">
                        <a href="#" @click.prevent="dashboardView = 'profile'" :class="{'text-blue-400': dashboardView === 'profile'}" class="block p-2 text-sm text-gray-400 rounded hover:bg-gray-700">Profile</a>
                        <a href="#" @click.prevent="dashboardView = 'wallet'" :class="{'text-blue-400': dashboardView === 'wallet'}" class="block p-2 text-sm text-gray-400 rounded hover:bg-gray-700">Wallet Address</a>
                    </div>
                </div>

                <a href="#" @click.prevent="dashboardView = 'refer'" :class="{'active': dashboardView === 'refer'}" class="sidebar-link"><i data-lucide="users" class="mr-3"></i> My Refer</a>
                <a href="#" @click.prevent="dashboardView = 'community'" :class="{'active': dashboardView === 'community'}" class="sidebar-link"><i data-lucide="network" class="mr-3"></i> My Community</a>
                <a href="#" @click.prevent="dashboardView = 'profit'" :class="{'active': dashboardView === 'profit'}" class="sidebar-link"><i data-lucide="dollar-sign" class="mr-3"></i> My Profit</a>
                <a href="#" @click.prevent="dashboardView = 'reward'" :class="{'active': dashboardView === 'reward'}" class="sidebar-link"><i data-lucide="award" class="mr-3"></i> Reward Income</a>

                <div x-data="{ open: false }">
                    <a href="#" @click.prevent="open = !open" class="sidebar-link justify-between">
                        <span class="flex items-center"><i data-lucide="landmark" class="mr-3"></i> Finance</span>
                        <i data-lucide="chevron-down" class="transition-transform" :class="{'rotate-180': open}"></i>
                    </a>
                    <div x-show="open" class="pl-6 space-y-1 mt-1">
                        <a href="#" @click.prevent="dashboardView = 'deposit'" :class="{'text-blue-400': dashboardView === 'deposit'}" class="block p-2 text-sm text-gray-400 rounded hover:bg-gray-700">Deposit</a>
                        <a href="#" @click.prevent="dashboardView = 'transfer'" :class="{'text-blue-400': dashboardView === 'transfer'}" class="block p-2 text-sm text-gray-400 rounded hover:bg-gray-700">Transfer</a>
                        <a href="#" @click.prevent="dashboardView = 'withdraw'" :class="{'text-blue-400': dashboardView === 'withdraw'}" class="block p-2 text-sm text-gray-400 rounded hover:bg-gray-700">Withdraw</a>
                    </div>
                </div>

                <a href="#" @click.prevent="dashboardView = 'support'" :class="{'active': dashboardView === 'support'}" class="sidebar-link"><i data-lucide="life-buoy" class="mr-3"></i> Support</a>
                <div class="!mt-auto">
                    <a href="#" @click.prevent="navigateTo('home')" class="sidebar-link bg-red-800/50 hover:bg-red-700/50"><i data-lucide="log-out" class="mr-3"></i> Logout</a>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 p-6 overflow-y-auto">
                <!-- Dashboard View -->
                <div id="dashboard-main" x-show="dashboardView === 'dashboard'">
                    <h1 class="text-3xl font-bold text-white">Dashboard</h1>
                    <p class="text-gray-400 mb-6">Welcome back, User!</p>
                    
                    <!-- Wallet Address View -->
                    <div id="dashboard-wallet" x-show="dashboardView === 'wallet'">
                        <h2 class="text-2xl font-bold text-white mb-4">Wallet Address</h2>
                        <div class="glass-card rounded-lg p-6 max-w-2xl">
                            <form class="space-y-4">
                                <p class="text-sm text-yellow-400 bg-yellow-900/50 p-3 rounded-lg">
                                    <strong>Reminder:</strong> Please add your USDT.BEP20 address carefully. Once added and confirmed with OTP, it cannot be changed.
                                </p>
                                <div>
                                    <label class="block text-sm font-medium text-gray-400">USDT.BEP20 Address</label>
                                    <input type="text" class="form-input mt-1" placeholder="Enter your wallet address" x-ref="walletAddressInput">
                                </div>
                                <div class="flex justify-end">
                                    <button type="button" @click="showOtpModal = true" class="btn-primary">Add Address</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
    function appState() {
        return {
            page: 'home',
            dashboardView: 'dashboard',
            showOtpModal: false,
            accountTypes: [
                { name: 'Trial', investment: '100 - 500', profit: 1, dailyValue: '1.00 - 5.00' },
                { name: 'Basic', investment: '501 - 1000', profit: 1.25, dailyValue: '6.26 - 12.50' },
                { name: 'Standard', investment: '1001 - 2500', profit: 1.50, dailyValue: '15.01 - 37.50' },
                { name: 'Advanced', investment: '2501 - 5000', profit: 1.75, dailyValue: '43.76 - 87.50' },
                { name: 'Pro', investment: '5001 - 10000', profit: 2.00, dailyValue: '100.02 - 200.00' },
                { name: 'VIP', investment: '10001 - 25000', profit: 2.25, dailyValue: '225.02 - 562.50' },
            ],
            init() {
                this.populateAccountCards();
                this.$watch('page', () => this.$nextTick(() => lucide.createIcons()));
                this.$watch('dashboardView', () => this.$nextTick(() => lucide.createIcons()));
                lucide.createIcons();
            },
            navigateTo(newPage) {
                this.page = newPage;
                window.scrollTo(0, 0);
            },
            populateAccountCards() {
                const container = document.getElementById('home-accounts-grid');
                if (!container) return;
                this.accountTypes.forEach(account => {
                    const card = document.createElement('div');
                    card.className = 'glass-card rounded-xl p-6 text-center hover:border-blue-500 transition-all transform hover:-translate-y-2';
                    card.innerHTML = `
                        <h3 class="text-2xl font-bold text-yellow-400 mb-2">${account.name}</h3>
                        <p class="text-gray-300 mb-4">Investment: <span class="font-semibold text-white">$${account.investment}</span></p>
                        <p class="text-4xl font-bold text-white mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-blue-600">${account.profit}%</p>
                        <p class="text-gray-400 mb-4">Daily Profit</p>
                        <p class="text-gray-300">Daily Value: <span class="font-semibold text-white">$${account.dailyValue}</span></p>
                    `;
                    container.appendChild(card);
                });
            }
        }
    }
    </script>
</body>
</html>