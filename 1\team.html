<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Investment Dashboard - Team</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .glassmorphism {
            background: rgba(31, 41, 55, 0.5); /* Semi-transparent dark background */
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        /* Style for breadcrumbs */
        .breadcrumb-link:not(:last-child)::after {
            content: '/';
            margin: 0 0.5rem;
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-gray-900 text-white">

    <!-- Top Header -->
    <header class="bg-gray-900/80 backdrop-blur-lg sticky top-0 z-40">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-amber-500 text-transparent bg-clip-text">
                    CryptoUI
                </div>
                <div class="flex items-center">
                    <nav class="hidden md:flex items-center space-x-8">
                        <a href="#" class="text-gray-300 hover:text-yellow-400 transition-colors">Dashboard</a>
                        <a href="#" class="text-gray-300 hover:text-yellow-400 transition-colors">Wallet</a>
                        <a href="#" class="text-yellow-400 transition-colors">Team</a>
                        <a href="#" class="text-gray-300 hover:text-yellow-400 transition-colors">Profile</a>
                    </nav>
                    <div class="flex items-center space-x-4 md:ml-8">
                        <button class="relative text-gray-300 hover:text-yellow-400 p-2 rounded-full hover:bg-gray-800">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path></svg>
                            <span class="absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-gray-900"></span>
                        </button>
                        <button class="flex text-sm border-2 border-transparent rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                            <img class="h-8 w-8 rounded-full" src="https://i.pravatar.cc/40?u=alexdoe" alt="User avatar" onerror="this.onerror=null;this.src='https://placehold.co/40x40/1f2937/ffffff?text=AD';">
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="container mx-auto p-4 pb-24">
        
        <div id="team-view">
            <!-- Header section with Title/Breadcrumbs on left and Details on right -->
            <div class="flex flex-row justify-between items-start mb-6">
                <!-- Left side: Title and Breadcrumbs -->
                <div class="flex-grow min-w-0"> <!-- Added min-w-0 for flexbox truncation -->
                    <h2 class="text-3xl font-bold mb-2 text-gray-100 truncate">My Team</h2>
                    <div id="breadcrumb-nav" class="text-lg text-gray-300 truncate"></div>
                </div>
                <!-- Right side: Leader Details (Desktop Only) -->
                <div id="header-details-container-desktop" class="hidden md:flex shrink-0 space-x-6 ml-4">
                    <!-- JS populates this for desktop -->
                </div>
            </div>

            <!-- Leader Details (Mobile Only) -->
            <div id="header-details-container-mobile" class="md:hidden -mt-2 mb-6">
                <!-- JS populates this for mobile -->
            </div>

            <div id="team-list-container" class="space-y-4">
                <!-- Team member cards will be dynamically inserted here -->
            </div>
        </div>

    </main>

    <!-- Bottom Navigation (Mobile Only) -->
  <nav
        class="md:hidden fixed bottom-0 left-0 right-0 bg-gray-900/80 backdrop-blur-lg border-t border-gray-700 p-2 flex justify-around z-50">
        <a href="dashboard.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6">
                </path>
            </svg>
            <span class="text-xs mt-1">Home</span>
        </a>
        <a href="wallet.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H4a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
            <span class="text-xs mt-1">Wallet</span>
        </a>
        <a href="team.html" class="flex flex-col items-center text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.653-.25-1.26-.698-1.702M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.653.25-1.26.698-1.702m12.302 0A5.002 5.002 0 0017 13h-2a5.002 5.002 0 00-4.302 2.298m-6.696 0A5.002 5.002 0 017 13H5a5.002 5.002 0 014.302-2.298M12 13a3 3 0 100-6 3 3 0 000 6z">
                </path>
            </svg>
            <span class="text-xs mt-1">Team</span>
        </a>
        <a href="profile.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="text-xs mt-1">Profile</span>
        </a>
    </nav>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // --- DATA ---
        const teamData = {
            'root': ['ayush', 'henil', 'shlok', 'tejas'],
            'ayush': { id: 'ayush', name: 'Ayush Patel', joinDate: '2024-02-20 14:15:23', status: 'Qualified: STAR', rank: 'STAR', avatar: 'https://i.pravatar.cc/150?u=ayush', borderColor: 'border-purple-500', rankIconType: 'star', investment: '1.25 BTC', teamSize: 12, downline: ['rohan', 'priya', 'amit'] },
            'henil': { id: 'henil', name: 'Henil Patel', joinDate: '2024-02-21 10:30:00', status: 'Qualified: PRO', rank: 'PRO', avatar: 'https://i.pravatar.cc/150?u=henil', borderColor: 'border-cyan-500', rankIconType: 'none', investment: '0.75 ETH', teamSize: 5, downline: ['sunita'] },
            'shlok': { id: 'shlok', name: 'Shlok Patel', joinDate: '2024-02-22 09:00:15', status: 'Qualified: ELITE', rank: 'ELITE', avatar: 'https://i.pravatar.cc/150?u=shlok', borderColor: 'border-pink-500', rankIconType: 'check', investment: '2,500 USDT', teamSize: 25, downline: [] },
            'tejas': { id: 'tejas', name: 'Tejas Patel', joinDate: '2024-02-23 18:45:50', status: 'Pending', rank: 'ROOKIE', avatar: 'https://i.pravatar.cc/150?u=tejas', borderColor: 'border-green-500', rankIconType: 'cross', investment: '500 ADA', teamSize: 1, downline: [] },
            'rohan': { id: 'rohan', name: 'Rohan Sharma', joinDate: '2024-03-01 11:00:00', status: 'Active', rank: 'PRO', avatar: 'https://i.pravatar.cc/150?u=rohan', borderColor: 'border-blue-500', rankIconType: 'check', investment: '0.1 BTC', teamSize: 2, downline: ['neha', 'vikas'] },
            'priya': { id: 'priya', name: 'Priya Singh', joinDate: '2024-03-02 12:30:00', status: 'Active', rank: 'PRO', avatar: 'https://i.pravatar.cc/150?u=priya', borderColor: 'border-indigo-500', rankIconType: 'check', investment: '0.2 BTC', teamSize: 1, downline: [] },
            'amit': { id: 'amit', name: 'Amit Kumar', joinDate: '2024-03-03 15:00:00', status: 'Inactive', rank: 'ROOKIE', avatar: 'https://i.pravatar.cc/150?u=amit', borderColor: 'border-gray-500', rankIconType: 'cross', investment: '0.05 BTC', teamSize: 0, downline: [] },
            'sunita': { id: 'sunita', name: 'Sunita Devi', joinDate: '2024-03-04 10:00:00', status: 'Active', rank: 'PRO', avatar: 'https://i.pravatar.cc/150?u=sunita', borderColor: 'border-rose-500', rankIconType: 'check', investment: '0.1 ETH', teamSize: 0, downline: [] },
            'neha': { id: 'neha', name: 'Neha Gupta', joinDate: '2024-04-10 09:00:00', status: 'Active', rank: 'ROOKIE', avatar: 'https://i.pravatar.cc/150?u=neha', borderColor: 'border-teal-500', rankIconType: 'check', investment: '1000 DOGE', teamSize: 0, downline: [] },
            'vikas': { id: 'vikas', name: 'Vikas Singh', joinDate: '2024-04-11 14:20:00', status: 'Active', rank: 'ROOKIE', avatar: 'https://i.pravatar.cc/150?u=vikas', borderColor: 'border-orange-500', rankIconType: 'check', investment: '50 DOT', teamSize: 0, downline: [] }
        };

        const rankIcons = {
            star: `<div class="w-12 h-12 flex items-center justify-center bg-yellow-400/20 rounded-full"><svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg></div>`,
            check: `<div class="w-12 h-12 flex items-center justify-center bg-blue-400/20 rounded-full"><svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20"><path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"></path></svg></div>`,
            cross: `<div class="w-12 h-12 flex items-center justify-center bg-red-400/20 rounded-full"><svg class="w-6 h-6 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg></div>`,
            none: `<div class="w-12 h-12 flex items-center justify-center bg-gray-400/20 rounded-full"><span class="text-gray-400 font-bold">N/A</span></div>`
        };

        // --- State ---
        let navigationStack = []; // e.g., ['ayush', 'rohan']

        // --- DOM Elements ---
        const teamListContainer = document.getElementById('team-list-container');
        const breadcrumbNav = document.getElementById('breadcrumb-nav');
        const headerDetailsContainerDesktop = document.getElementById('header-details-container-desktop');
        const headerDetailsContainerMobile = document.getElementById('header-details-container-mobile');
        const mainTitle = document.querySelector('#team-view h2');

        // --- Functions ---
        
        function renderView() {
            const currentLevel = navigationStack.length;
            const currentLeaderId = currentLevel > 0 ? navigationStack[currentLevel - 1] : 'root';
            const leaderData = teamData[currentLeaderId];

            renderBreadcrumbs();
            
            if(currentLeaderId !== 'root') {
                 mainTitle.textContent = `${leaderData.name}'s Team`;
                 renderHeaderDetails(leaderData);
            } else {
                 mainTitle.textContent = 'My Team';
                 renderHeaderDetails(null);
            }

            const membersToShowIds = leaderData?.downline || teamData.root;
            const membersToShow = membersToShowIds.map(id => teamData[id]);
            renderTeamList(membersToShow, currentLevel);
        }

        function renderBreadcrumbs() {
            let breadcrumbHtml = `<a href="#" class="breadcrumb-link hover:text-yellow-400" data-level="-1">My Team</a>`;
            navigationStack.forEach((memberId, index) => {
                const member = teamData[memberId];
                breadcrumbHtml += `<a href="#" class="breadcrumb-link hover:text-yellow-400" data-level="${index}">${member.name}</a>`;
            });
            breadcrumbNav.innerHTML = breadcrumbHtml;
        }

        function renderTeamList(members, level) {
            let content = '';
            if (members.length === 0) {
                content = '<p class="text-gray-400 text-center py-8">This team member has no downline yet.</p>';
            } else {
                 for (const member of members) {
                    const isClickable = level < 2 && member.downline && member.downline.length > 0;
                    const cursorClass = isClickable ? 'cursor-pointer' : 'cursor-default';
                    const hoverClass = isClickable ? 'hover:bg-gray-700/50' : '';
                    
                    content += `
                    <div class="member-card block ${cursorClass} ${hoverClass} rounded-2xl transition-colors" data-id="${member.id}" data-clickable="${isClickable}">
                        <div class="glassmorphism bg-gray-800 rounded-2xl shadow-lg p-4 flex items-center justify-between">
                            <div class="flex items-center space-x-4 min-w-0">
                                <img class="w-12 h-12 rounded-full border-2 ${member.borderColor}" src="${member.avatar}" alt="${member.name}" onerror="this.onerror=null;this.src='https://placehold.co/150x150/1f2937/ffffff?text=${member.name.charAt(0)}';">
                                <div class="min-w-0">
                                    <h3 class="text-lg font-semibold text-white truncate">${member.name}</h3>
                                    <p class="text-sm text-gray-400">${member.joinDate}</p>
                                    <p class="text-sm text-yellow-400 truncate">${member.status}</p>
                                </div>
                            </div>
                            <div class="shrink-0 ml-4">
                                ${rankIcons[member.rankIconType] || rankIcons.none}
                            </div>
                        </div>
                    </div>`;
                }
            }
            teamListContainer.innerHTML = content;
        }
        
        function renderHeaderDetails(member) {
            if (!member) {
                headerDetailsContainerDesktop.innerHTML = '';
                headerDetailsContainerMobile.innerHTML = '';
                return;
            };

            const desktopContent = `
                <div class="text-right">
                    <p class="text-sm text-gray-400">Total Investment</p>
                    <p class="text-lg font-bold text-white">${member.investment}</p>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-400">Team Size</p>
                    <p class="text-lg font-bold text-white">${member.teamSize}</p>
                </div>
            `;

            const mobileContent = `
                <div class="glassmorphism rounded-xl p-4 flex justify-around text-center">
                    <div>
                        <p class="text-sm text-gray-400">Total Investment</p>
                        <p class="text-lg font-bold text-white">${member.investment}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-400">Team Size</p>
                        <p class="text-lg font-bold text-white">${member.teamSize}</p>
                    </div>
                </div>
            `;
            
            headerDetailsContainerDesktop.innerHTML = desktopContent;
            headerDetailsContainerMobile.innerHTML = mobileContent;
        }

        // --- Event Listeners ---
        teamListContainer.addEventListener('click', function(event) {
            const card = event.target.closest('.member-card');
            if (card && card.dataset.clickable === 'true') {
                const memberId = card.dataset.id;
                navigationStack.push(memberId);
                renderView();
            }
        });

        breadcrumbNav.addEventListener('click', function(event) {
            event.preventDefault();
            const link = event.target.closest('.breadcrumb-link');
            if(link) {
                const level = parseInt(link.dataset.level, 10);
                navigationStack = navigationStack.slice(0, level + 1);
                renderView();
            }
        });

        // --- Initial Load ---
        renderView();
    });
</script>

</body>
</html>
