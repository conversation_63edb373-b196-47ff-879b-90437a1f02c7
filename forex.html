<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forex Cross Rates - CryptoInvest Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .glassmorphism {
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* Dark Premium Theme */
        .theme-dark-premium {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: white;
        }

        .theme-dark-premium .card {
            background: rgba(22, 33, 62, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .theme-dark-premium .accent {
            color: #ffd700;
        }

        .theme-dark-premium .secondary {
            color: #f4d03f;
        }

        /* Professional Header Enhancements */
        header {
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .logo-glow {
            position: relative;
            transition: all 0.3s ease;
        }

        .logo-glow::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 14px;
            background: linear-gradient(45deg, transparent, #ffd700, transparent);
            opacity: 0.1;
            animation: logoRotate 4s linear infinite;
            z-index: -1;
        }

        .logo-glow:hover::before {
            opacity: 0.4;
        }

        @keyframes logoRotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .logo-svg {
            transition: all 0.3s ease;
        }

        .logo-bg {
            fill: #ffd700;
            transition: fill 0.3s ease;
        }

        .logo-symbol {
            fill: #000000;
            transition: fill 0.3s ease;
        }

        .widget-container {
            min-height: 600px;
            border-radius: 12px;
            overflow: hidden;
        }
    </style>
</head>

<body class="theme-dark-premium min-h-screen">
    <!-- Header -->
    <header class="sticky top-0 z-50 backdrop-blur-xl bg-black bg-opacity-40 border-b border-white border-opacity-10">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <!-- Logo & Brand -->
                <div class="flex items-center space-x-3">
                    <div
                        class="w-10 h-10 rounded-xl flex items-center justify-center shadow-lg logo-glow relative overflow-hidden">
                        <svg class="w-8 h-8 logo-svg" viewBox="0 0 4091.27 4091.73" xmlns="http://www.w3.org/2000/svg">
                            <g>
                                <circle cx="2045.635" cy="2045.865" r="2045.635" class="logo-bg" />
                                <path class="logo-symbol" fill-rule="nonzero"
                                    d="M2947.77 1754.38c40.72,-272.26 -166.56,-418.61 -450,-516.24l91.95 -368.8 -224.5 -55.94 -89.51 359.09c-59.02,-14.72 -119.63,-28.59 -179.87,-42.34l90.16 -361.46 -224.36 -55.94 -92 368.68c-48.84,-11.12 -96.81,-22.11 -143.35,-33.69l0.26 -1.16 -309.59 -77.31 -59.72 239.78c0,0 166.56,38.18 163.05,40.53 90.91,22.69 107.35,82.87 104.62,130.57l-104.74 420.15c6.26,1.59 14.38,3.89 23.34,7.49 -7.49,-1.86 -15.46,-3.89 -23.73,-5.87l-146.81 588.57c-11.11,27.62 -39.31,69.07 -102.87,53.33 2.25,3.26 -163.17,-40.72 -163.17,-40.72l-111.46 256.98 292.15 72.83c54.35,13.63 107.61,27.89 160.06,41.3l-92.9 373.03 224.24 55.94 92 -369.07c61.26,16.63 120.71,31.97 178.91,46.43l-91.69 367.33 224.51 55.94 92.89 -372.33c382.82,72.45 670.67,43.24 791.83,-303.02 97.63,-278.78 -4.86,-439.58 -206.26,-544.44 146.69,-33.83 257.18,-130.31 286.64,-329.61l-0.07 -0.05zm-512.93 719.26c-69.38,278.78 -538.76,128.08 -690.94,90.29l123.28 -494.2c152.17,37.99 640.17,113.17 567.67,403.91zm69.43 -723.3c-63.29,253.58 -453.96,124.75 -580.69,93.16l111.77 -448.21c126.73,31.59 534.85,90.55 468.94,355.05l-0.02 0z" />
                            </g>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold accent">CryptoInvest</h1>
                        <p class="text-xs text-gray-400 font-medium">Professional Trading</p>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="index.html"
                        class="text-sm font-medium text-gray-300 hover:text-white transition-colors duration-200">Dashboard</a>
                    <a href="theme_showcase.html"
                        class="text-sm font-medium text-gray-300 hover:text-white transition-colors duration-200">Themes</a>
                    <a href="forex.html" class="text-sm font-medium accent">Forex</a>
                </nav>

                <!-- Right Actions -->
                <div class="flex items-center space-x-4">
                    <button
                        class="w-9 h-9 rounded-lg hover:bg-white hover:bg-opacity-10 flex items-center justify-center transition-all duration-200">
                        <i class="fas fa-bell text-sm accent"></i>
                    </button>
                    <button class="w-10 h-10 accent rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-user text-sm text-black"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold accent mb-2">Forex Cross Rates</h2>
            <p class="text-gray-400">Real-time foreign exchange rates and currency pairs</p>
        </div>

        <!-- Forex Widget Container -->
        <div class="card glassmorphism p-6 rounded-lg">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 accent rounded-full flex items-center justify-center">
                        <i class="fas fa-chart-line text-xl text-black"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold">Live Forex Rates</h3>
                        <p class="text-gray-400 text-sm">Major currency pairs</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-400">Powered by</p>
                    <p class="text-sm font-semibold accent">Alyanka</p>
                </div>
            </div>

            <!-- TradingView Widget -->
            <div class="widget-container">
                <iframe scrolling="no" allowtransparency="true" frameborder="0"
                    src="https://www.tradingview-widget.com/embed-widget/forex-cross-rates/?locale=en#%7B%22width%22%3A%22100%25%22%2C%22height%22%3A%22100%25%22%2C%22currencies%22%3A%5B%22EUR%22%2C%22USD%22%2C%22JPY%22%2C%22GBP%22%2C%22CHF%22%2C%22AUD%22%2C%22CAD%22%2C%22NZD%22%2C%22CNY%22%5D%2C%22colorTheme%22%3A%22dark%22%2C%22utm_source%22%3A%22botalpha.me%22%2C%22utm_medium%22%3A%22widget%22%2C%22utm_campaign%22%3A%22forex-cross-rates%22%2C%22page-uri%22%3A%22botalpha.me%2Fpartner%2Fdashboard%22%7D"
                    title="forex cross-rates TradingView widget" lang="en"
                    style="user-select: none; box-sizing: border-box; display: block; height: 600px; width: 100%;">
                </iframe>
            </div>
        </div>

        <!-- Additional Info Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div class="card glassmorphism p-6 rounded-lg text-center">
                <i class="fas fa-globe text-3xl accent mb-4"></i>
                <h4 class="font-semibold mb-2">Global Markets</h4>
                <p class="text-gray-400 text-sm">Access major currency pairs from around the world</p>
            </div>
            <div class="card glassmorphism p-6 rounded-lg text-center">
                <i class="fas fa-clock text-3xl accent mb-4"></i>
                <h4 class="font-semibold mb-2">Real-Time Data</h4>
                <p class="text-gray-400 text-sm">Live forex rates updated in real-time</p>
            </div>
            <div class="card glassmorphism p-6 rounded-lg text-center">
                <i class="fas fa-chart-bar text-3xl accent mb-4"></i>
                <h4 class="font-semibold mb-2">Professional Tools</h4>
                <p class="text-gray-400 text-sm">Advanced trading tools and analytics</p>
            </div>
        </div>
    </main>
</body>

</html>