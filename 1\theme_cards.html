<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto UI Theme Cards</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            -webkit-backdrop-filter: blur(12px);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        body {
            background-image: url('https://www.transparenttextures.com/patterns/cubes.png');
        }
    </style>
</head>
<body class="bg-gray-900 text-white p-8 font-sans">

    <h1 class="text-4xl font-bold mb-12 text-center tracking-wider">Crypto UI Theme Cards</h1>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">

        <!-- Ethereum Card -->
        <div class="rounded-2xl p-6 bg-gradient-to-br from-indigo-900 to-purple-900">
            <h2 class="text-2xl font-bold mb-6 border-b-2 pb-3 border-purple-400 text-purple-300">Ethereum Theme</h2>
            <div class="space-y-6">
                <div class="glass-card rounded-xl p-4 border border-purple-500/50">
                    <h3 class="font-semibold text-lg mb-3 text-purple-300">UI Elements</h3>
                    <div class="space-y-4">
                        <button class="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold py-2 px-4 rounded-lg transition-all">Connect Wallet</button>
                        <div class="bg-purple-900/50 p-3 rounded-lg">
                            <p class="text-purple-200">Balance: <span class="font-bold text-white">1.25 ETH</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Oceanic Card -->
        <div class="rounded-2xl p-6 bg-gradient-to-br from-cyan-900 to-blue-900">
            <h2 class="text-2xl font-bold mb-6 border-b-2 pb-3 border-teal-400 text-teal-300">Oceanic Theme</h2>
            <div class="space-y-6">
                <div class="glass-card rounded-xl p-4 border border-teal-500/50">
                    <h3 class="font-semibold text-lg mb-3 text-teal-300">UI Elements</h3>
                    <div class="space-y-4">
                        <button class="w-full bg-gradient-to-r from-blue-600 to-teal-500 hover:from-blue-700 hover:to-teal-600 text-white font-bold py-2 px-4 rounded-lg transition-all">Trade Now</button>
                        <div class="bg-teal-400/20 p-3 rounded-lg">
                            <p class="text-teal-200">Market is <span class="font-bold text-white">bullish</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bitcoin Card -->
        <div class="rounded-2xl p-6 bg-gradient-to-br from-gray-900 to-orange-900">
            <h2 class="text-2xl font-bold mb-6 border-b-2 pb-3 border-yellow-400 text-yellow-300">Bitcoin Theme</h2>
            <div class="space-y-6">
                <div class="glass-card rounded-xl p-4 border border-yellow-500/50">
                    <h3 class="font-semibold text-lg mb-3 text-yellow-300">UI Elements</h3>
                    <div class="space-y-4">
                        <button class="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-gray-900 font-bold py-2 px-4 rounded-lg transition-all">Deposit BTC</button>
                        <div class="bg-orange-500/20 p-3 rounded-lg">
                            <p class="text-orange-200">Network Fee: <span class="font-bold text-white">0.0001 BTC</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Silver Card -->
        <div class="rounded-2xl p-6 bg-gradient-to-br from-slate-800 to-gray-800">
            <h2 class="text-2xl font-bold mb-6 border-b-2 pb-3 border-slate-400 text-slate-300">Silver Theme</h2>
            <div class="space-y-6">
                <div class="glass-card rounded-xl p-4 border border-slate-500/50">
                    <h3 class="font-semibold text-lg mb-3 text-slate-300">UI Elements</h3>
                    <div class="space-y-4">
                        <button class="w-full bg-gradient-to-r from-slate-500 to-gray-500 hover:from-slate-600 hover:to-gray-600 text-white font-bold py-2 px-4 rounded-lg transition-all">View Transaction</button>
                        <div class="bg-slate-300/20 p-3 rounded-lg">
                            <p class="text-slate-200">Status: <span class="font-bold text-white">Confirmed</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

</body>
</html>