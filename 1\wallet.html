<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Investment Dashboard - Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .glassmorphism {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body class="bg-gray-900 text-white">

    <!-- Top Header -->
    <div class="bg-gray-900/80 backdrop-blur-lg sticky top-0 z-40">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-amber-500 text-transparent bg-clip-text">
                    CryptoUI
                </div>
                <div class="flex items-center">
                    <nav class="hidden md:flex items-center space-x-8">
                        <a href="dashboard.html" class="text-gray-300 hover:text-yellow-400 transition-colors">Dashboard</a>
                        <a href="wallet.html" class="text-yellow-400 transition-colors">Wallet</a>
                        <a href="team.html" class="text-gray-300 hover:text-yellow-400 transition-colors">Team</a>
                        <a href="profile.html" class="text-gray-300 hover:text-yellow-400 transition-colors">Profile</a>
                    </nav>
                    <div class="flex items-center space-x-4 md:ml-8">
                        <button class="relative text-gray-300 hover:text-yellow-400 p-2 rounded-full hover:bg-gray-800">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path></svg>
                            <span class="absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-gray-900"></span>
                        </button>
                        <button class="flex text-sm border-2 border-transparent rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                            <img class="h-8 w-8 rounded-full" src="https://i.pravatar.cc/40?u=alexdoe" alt="User avatar">
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto p-4 pb-24">
        <main class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="md:col-span-3 space-y-8">
                <section id="deposit" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800">
                    <h3 class="text-xl font-semibold mb-4 text-gray-200">Deposit</h3>
                    <form class="space-y-4">
                        <div>
                            <label for="deposit-amount" class="block text-sm font-medium text-gray-400">Amount</label>
                            <input type="number" id="deposit-amount" class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-yellow-500 focus:border-yellow-500 sm:text-sm" placeholder="Enter amount">
                        </div>
                        <button type="submit" class="w-full bg-gradient-to-r from-green-500 to-teal-500 text-white font-bold py-2 px-4 rounded-md hover:opacity-90 transition-transform transform hover:scale-105">Deposit Now</button>
                    </form>
                    <div class="mt-6">
                        <h4 class="text-lg font-semibold text-gray-300">Transaction Report</h4>
                        <ul id="deposit-report" class="mt-2 space-y-2 text-gray-400">
                            <li class="text-sm">Deposited: $500 on 01/07/2023, 10:30:00</li>
                        </ul>
                    </div>
                </section>

                <section id="top-up" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800">
                    <h3 class="text-xl font-semibold mb-4 text-gray-200">Top-up Account</h3>
                    <form class="space-y-4">
                        <div>
                            <label for="topup-amount" class="block text-sm font-medium text-gray-400">Amount</label>
                            <input type="number" id="topup-amount" class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-yellow-500 focus:border-yellow-500 sm:text-sm" placeholder="Enter amount">
                        </div>
                        <button type="submit" class="w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-bold py-2 px-4 rounded-md hover:opacity-90 transition-transform transform hover:scale-105">Top-up</button>
                    </form>
                    <div class="mt-6">
                        <h4 class="text-lg font-semibold text-gray-300">History</h4>
                        <ul id="topup-report" class="mt-2 space-y-2 text-gray-400">
                            <li class="text-sm">Top-up: $100 on 20/06/2023, 15:00:00</li>
                        </ul>
                    </div>
                </section>

                <section id="stacking" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800">
                    <h3 class="text-xl font-semibold mb-2 text-gray-200">Stacking BTC</h3>
                    <p class="text-sm text-gray-400 mb-4">Stacking Coin time minimum 6 months (withdraw after 6 months)</p>
                    <form class="space-y-4">
                        <div>
                            <label for="stacking-amount" class="block text-sm font-medium text-gray-400">Amount</label>
                            <input type="number" id="stacking-amount" class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-yellow-500 focus:border-yellow-500 sm:text-sm" placeholder="Enter amount to stack">
                        </div>
                        <button type="submit" class="w-full bg-gradient-to-r from-red-500 to-orange-500 text-white font-bold py-2 px-4 rounded-md hover:opacity-90 transition-transform transform hover:scale-105">Stack BTC</button>
                    </form>
                    <div class="mt-6">
                        <h4 class="text-lg font-semibold text-gray-300">Transaction Report</h4>
                        <ul id="stacking-report" class="mt-2 space-y-2 text-gray-400">
                            <li class="text-sm">Stacked: 10 BTC on 01/02/2023, 18:00:00</li>
                        </ul>
                    </div>
                </section>

                <section id="transfer" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800">
                    <h3 class="text-xl font-semibold mb-4 text-gray-200">Transfer</h3>
                    <form class="space-y-4">
                        <div>
                            <label for="transfer-address" class="block text-sm font-medium text-gray-400">Recipient Address</label>
                            <input type="text" id="transfer-address" class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-yellow-500 focus:border-yellow-500 sm:text-sm" placeholder="Enter recipient address">
                        </div>
                        <div>
                            <label for="transfer-amount" class="block text-sm font-medium text-gray-400">Amount</label>
                            <input type="number" id="transfer-amount" class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-yellow-500 focus:border-yellow-500 sm:text-sm" placeholder="Enter amount">
                        </div>
                        <button type="submit" class="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold py-2 px-4 rounded-md hover:opacity-90 transition-transform transform hover:scale-105">Transfer</button>
                    </form>
                    <div class="mt-6">
                        <h4 class="text-lg font-semibold text-gray-300">History</h4>
                        <ul id="transfer-report" class="mt-2 space-y-2 text-gray-400">
                            <li class="text-sm">No transfers yet.</li>
                        </ul>
                    </div>
                </section>

           
            </div>
        </main>
    </div>

    <!-- Bottom Navigation (Mobile Only) -->
    <nav
        class="md:hidden fixed bottom-0 left-0 right-0 bg-gray-900/80 backdrop-blur-lg border-t border-gray-700 p-2 flex justify-around z-50">
        <a href="dashboard.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6">
                </path>
            </svg>
            <span class="text-xs mt-1">Home</span>
        </a>
        <a href="wallet.html" class="flex flex-col items-center text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H4a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
            <span class="text-xs mt-1">Wallet</span>
        </a>
        <a href="team.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.653-.25-1.26-.698-1.702M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.653.25-1.26.698-1.702m12.302 0A5.002 5.002 0 0017 13h-2a5.002 5.002 0 00-4.302 2.298m-6.696 0A5.002 5.002 0 017 13H5a5.002 5.002 0 014.302-2.298M12 13a3 3 0 100-6 3 3 0 000 6z">
                </path>
            </svg>
            <span class="text-xs mt-1">Team</span>
        </a>
        <a href="profile.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="text-xs mt-1">Profile</span>
        </a>
    </nav>
    
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            function handleTransaction(form, input, reportList, transactionText) {
                form.addEventListener('submit', function (e) {
                    e.preventDefault();
                    const amount = input.value.trim();
                    if (amount) {
                        const report = document.getElementById(reportList);
                        const newEntry = document.createElement('li');
                        const date = new Date().toLocaleString();
                        newEntry.textContent = `${transactionText} ${amount} on ${date}`;
                        newEntry.className = 'text-sm';
                        if (report.children.length === 1 && report.children[0].textContent.includes('No')) {
                            report.innerHTML = '';
                        }
                        report.prepend(newEntry);
                        input.value = '';
                    }
                });
            }
            
            const depositForm = document.querySelector('#deposit form');
            const depositAmountInput = document.getElementById('deposit-amount');
            handleTransaction(depositForm, depositAmountInput, 'deposit-report', 'Deposited: $');

            const topupForm = document.querySelector('#top-up form');
            const topupAmountInput = document.getElementById('topup-amount');
            handleTransaction(topupForm, topupAmountInput, 'topup-report', 'Topped up: $');

            const stackingForm = document.querySelector('#stacking form');
            const stackingAmountInput = document.getElementById('stacking-amount');
            handleTransaction(stackingForm, stackingAmountInput, 'stacking-report', 'Stacked:');

            const transferForm = document.querySelector('#transfer form');
            transferForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const recipientInput = document.getElementById('transfer-address');
                const amountInput = document.getElementById('transfer-amount');
                const recipient = recipientInput.value.trim();
                const amount = amountInput.value.trim();

                if (recipient && amount) {
                    const report = document.getElementById('transfer-report');
                    const newEntry = document.createElement('li');
                    const date = new Date().toLocaleString();
                    newEntry.textContent = `Transferred ${amount} to ${recipient} on ${date}`;
                    newEntry.className = 'text-sm';

                    if (report.children.length === 1 && report.children[0].textContent.includes('No')) {
                        report.innerHTML = '';
                    }

                    report.prepend(newEntry);
                    recipientInput.value = '';
                    amountInput.value = '';
                }
            });
        });
    </script>

</body>
</html>