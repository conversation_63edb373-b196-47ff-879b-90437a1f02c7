<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Investment Dashboard - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #111827;
            /* bg-gray-900 */
        }

        .glassmorphism {
            background: rgba(31, 41, 55, 0.5);
            /* bg-gray-800 with opacity */
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #111827;
        }

        ::-webkit-scrollbar-thumb {
            background: #374151;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #4b5563;
        }
    </style>
</head>

<body class="bg-gray-900 text-white">

    <!-- Top Header -->
    <div class="bg-gray-900/80 backdrop-blur-lg sticky top-0 z-40">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div
                    class="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-amber-500 text-transparent bg-clip-text">
                    CryptoUI
                </div>
                <div class="flex items-center">
                    <nav class="hidden md:flex items-center space-x-8">
                        <a href="#" class="text-yellow-400 transition-colors">Dashboard</a>
                        <a href="#" class="text-gray-300 hover:text-yellow-400 transition-colors">Wallet</a>
                        <a href="#" class="text-gray-300 hover:text-yellow-400 transition-colors">Team</a>
                        <a href="#" class="text-gray-300 hover:text-yellow-400 transition-colors">Profile</a>
                    </nav>
                    <div class="flex items-center space-x-4 md:ml-8">
                        <button class="relative text-gray-300 hover:text-yellow-400 p-2 rounded-full hover:bg-gray-800">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9">
                                </path>
                            </svg>
                            <span
                                class="absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-gray-900"></span>
                        </button>
                        <button
                            class="flex text-sm border-2 border-transparent rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                            <img class="h-8 w-8 rounded-full" src="https://i.pravatar.cc/40?u=alexdoe"
                                alt="User avatar">
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto p-4 pb-24">
        <header class="mb-8">
            <div class="text-center p-2 rounded-lg mb-4 bg-gradient-to-r from-yellow-300 via-amber-500 to-yellow-300">
                <p class="font-bold text-black text-lg">🚨 Offer Message Highlight 🚨</p>
            </div>
            <div class="bg-gray-800 p-4 rounded-lg glassmorphism">
                <h2 class="text-xl font-semibold mb-2 text-gray-200">Company Message Board</h2>
                <p class="text-gray-400">Welcome to the future of crypto investment. We are here to guide you on your
                    journey to financial freedom.</p>
            </div>
        </header>

        <main class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="md:col-span-3 space-y-8">
                <section id="package-info" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800/50">
                    <h3 class="text-xl font-semibold mb-2 text-gray-200">Selected Package</h3>
                    <p class="text-2xl font-bold text-yellow-400">Trial Package ($10)</p>
                </section>

                <section id="profit-info" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800/50">
                    <h3 class="text-xl font-semibold mb-2 text-gray-200">Daily Profit</h3>
                    <p class="text-2xl font-bold text-green-400">$1.25</p>
                </section>

                <section id="balance-info" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800/50">
                    <h3 class="text-xl font-semibold mb-2 text-gray-200">Total Available Balance</h3>
                    <p class="text-2xl font-bold text-blue-400">$1,234.56</p>
                </section>

                <section id="profit-history" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800/50">
                    <h3 class="text-xl font-semibold mb-4 text-gray-200">Last 5 Days Profit</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex justify-between"><span>2025-06-30:</span> <span
                                class="font-semibold text-green-400">$1.25</span></li>
                        <li class="flex justify-between"><span>2025-06-29:</span> <span
                                class="font-semibold text-green-400">$1.25</span></li>
                        <li class="flex justify-between"><span>2025-06-28:</span> <span
                                class="font-semibold text-green-400">$1.25</span></li>
                        <li class="flex justify-between"><span>2025-06-27:</span> <span
                                class="font-semibold text-green-400">$1.25</span></li>
                        <li class="flex justify-between"><span>2025-06-26:</span> <span
                                class="font-semibold text-green-400">$1.25</span></li>
                    </ul>
                </section>

                <section id="btc-card"
                    class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800/50 flex items-center space-x-6">
                    <img src="https://i.gifer.com/origin/a7/a7b537940905b94419165334754b4887_w200.gif" alt="BTC"
                        class="w-20 h-20">
                    <div>
                        <h3 class="text-2xl font-bold text-white">cyt coin</h3>
                        <p class="text-lg text-gray-300">Current Rate: <span
                                class="font-semibold text-green-400">$0.45</span></p>
                        <p class="text-sm text-gray-400">Stacking: <span
                                class="font-semibold text-yellow-400">Yes</span></p>
                    </div>
                </section>

                <section id="stacked-coin-info" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800/50">
                    <h3 class="text-xl font-semibold mb-2 text-gray-200">Stacked Coin (BTC)</h3>
                    <div class="flex justify-between items-center">
                        <p class="text-2xl font-bold text-white">$10,000.00</p>
                        <p class="text-lg font-semibold text-green-400">+5.2%</p>
                    </div>
                </section>

                <section id="actions" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800/50">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-center">
                        <a href="#"
                            class="bg-gradient-to-r from-green-500 to-teal-500 text-white font-bold py-3 px-4 rounded-md hover:opacity-90 transition-transform transform hover:scale-105 block">
                            Add Fund
                        </a>
                        <a href="#"
                            class="bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-bold py-3 px-4 rounded-md hover:opacity-90 transition-transform transform hover:scale-105 block">
                            Top-up
                        </a>
                        <a href="#"
                            class="bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold py-3 px-4 rounded-md hover:opacity-90 transition-transform transform hover:scale-105 block">
                            Team
                        </a>
                        <a href="#"
                            class="bg-gradient-to-r from-red-500 to-orange-500 text-white font-bold py-3 px-4 rounded-md hover:opacity-90 transition-transform transform hover:scale-105 block">
                            Add Stacking Coin
                        </a>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Bottom Navigation (Mobile Only) -->
    <nav
        class="md:hidden fixed bottom-0 left-0 right-0 bg-gray-900/80 backdrop-blur-lg border-t border-gray-700 p-2 flex justify-around z-50">
        <a href="dashboard.html" class="flex flex-col items-center text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6">
                </path>
            </svg>
            <span class="text-xs mt-1">Home</span>
        </a>
        <a href="wallet.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H4a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
            <span class="text-xs mt-1">Wallet</span>
        </a>
        <a href="team.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.653-.25-1.26-.698-1.702M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.653.25-1.26.698-1.702m12.302 0A5.002 5.002 0 0017 13h-2a5.002 5.002 0 00-4.302 2.298m-6.696 0A5.002 5.002 0 017 13H5a5.002 5.002 0 014.302-2.298M12 13a3 3 0 100-6 3 3 0 000 6z">
                </path>
            </svg>
            <span class="text-xs mt-1">Team</span>
        </a>
        <a href="profile.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="text-xs mt-1">Profile</span>
        </a>
    </nav>

</body>

</html>