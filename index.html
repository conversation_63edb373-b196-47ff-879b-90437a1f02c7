<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoInvest Pro - Premium Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'crypto-dark': '#0a0a0a',
                        'crypto-card': '#1a1a1a',
                        'crypto-gold': '#ffd700',
                        'crypto-purple': '#6b46c1',
                    },
                    backgroundImage: {
                        'gradient-gold': 'linear-gradient(135deg, #ffd700 0%, #ffb347 100%)',
                        'gradient-purple': 'linear-gradient(135deg, #6b46c1 0%, #9333ea 100%)',
                        'gradient-dark': 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .glassmorphism {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .hover-glow:hover {
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        .coin-pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .gradient-text {
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-crypto-dark text-white min-h-screen">
    <!-- Header Section -->
    <header class="glassmorphism sticky top-0 z-50 p-4">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold gradient-text">CryptoInvest Pro</h1>
            <div class="flex items-center space-x-4">
                <i class="fas fa-bell text-crypto-gold text-xl"></i>
                <i class="fas fa-user-circle text-2xl"></i>
            </div>
        </div>
    </header>

    <!-- Offer Message Highlight -->
    <section class="p-4">
        <div class="container mx-auto">
            <div class="bg-gradient-gold text-black p-4 rounded-lg text-center font-bold text-lg shadow-lg">
                🚨 LIMITED TIME OFFER: Get 20% Bonus on First Deposit! 🚨
            </div>
        </div>
    </section>

    <!-- Company Message Board -->
    <section class="p-4">
        <div class="container mx-auto">
            <div class="glassmorphism p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4 gradient-text">📢 Company Announcements</h2>
                <div class="space-y-3">
                    <p class="text-gray-300">• New staking rewards program launched with higher APY</p>
                    <p class="text-gray-300">• Mobile app update available with enhanced security</p>
                    <p class="text-gray-300">• Weekly market analysis reports now available</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Dashboard Grid -->
    <main class="p-4 pb-20">
        <div class="container mx-auto grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            
            <!-- BTC Coin Card -->
            <div class="glassmorphism p-6 rounded-lg hover-glow">
                <div class="text-center">
                    <div class="coin-pulse mb-4">
                        <img src="https://cryptologos.cc/logos/bitcoin-btc-logo.png" alt="BTC" class="w-16 h-16 mx-auto">
                    </div>
                    <h3 class="text-xl font-bold mb-2">BTC Coin 🪙</h3>
                    <p class="text-2xl font-bold text-crypto-gold mb-2">$0.45</p>
                    <p class="text-gray-400">Stacking: 10 BTC ($4.5)</p>
                </div>
            </div>

            <!-- User Information Card -->
            <div class="glassmorphism p-6 rounded-lg">
                <h3 class="text-xl font-semibold mb-4 gradient-text">👤 User Information</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-400">User Name:</span>
                        <span id="userName">John Doe</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">User ID:</span>
                        <span id="userId">CRY001234</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Register Date:</span>
                        <span id="registerDate">2024-01-15</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Topup Date:</span>
                        <span id="topupDate">2024-06-20</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Account Type:</span>
                        <span class="text-crypto-gold font-semibold">Trial ($10)</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Wallet Balance:</span>
                        <span class="text-crypto-gold font-bold" id="walletBalance">$125.50</span>
                    </div>
                </div>
            </div>

            <!-- Profit Information Card -->
            <div class="glassmorphism p-6 rounded-lg">
                <h3 class="text-xl font-semibold mb-4 gradient-text">📈 Profit Information</h3>
                <div class="space-y-4">
                    <div class="text-center">
                        <p class="text-gray-400 mb-1">Today's Profit</p>
                        <p class="text-2xl font-bold text-green-400" id="todayProfit">+$12.45</p>
                    </div>
                    <div class="text-center">
                        <p class="text-gray-400 mb-1">Total Profit</p>
                        <p class="text-3xl font-bold text-crypto-gold" id="totalProfit">$1,234.56</p>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <!-- Navigation Tabs -->
    <nav class="fixed bottom-0 left-0 right-0 glassmorphism p-4">
        <div class="container mx-auto">
            <div class="flex justify-around">
                <button onclick="showSection('refer')" class="nav-btn flex flex-col items-center space-y-1 p-2 rounded-lg hover:bg-crypto-gold hover:text-black transition-all">
                    <i class="fas fa-share-alt text-xl"></i>
                    <span class="text-xs">Refer</span>
                </button>
                <button onclick="showSection('deposit')" class="nav-btn flex flex-col items-center space-y-1 p-2 rounded-lg hover:bg-crypto-gold hover:text-black transition-all">
                    <i class="fas fa-plus-circle text-xl"></i>
                    <span class="text-xs">Deposit</span>
                </button>
                <button onclick="showSection('topup')" class="nav-btn flex flex-col items-center space-y-1 p-2 rounded-lg hover:bg-crypto-gold hover:text-black transition-all">
                    <i class="fas fa-wallet text-xl"></i>
                    <span class="text-xs">Top-up</span>
                </button>
                <button onclick="showSection('transfer')" class="nav-btn flex flex-col items-center space-y-1 p-2 rounded-lg hover:bg-crypto-gold hover:text-black transition-all">
                    <i class="fas fa-exchange-alt text-xl"></i>
                    <span class="text-xs">Transfer</span>
                </button>
                <button onclick="showSection('stacking')" class="nav-btn flex flex-col items-center space-y-1 p-2 rounded-lg hover:bg-crypto-gold hover:text-black transition-all">
                    <i class="fas fa-coins text-xl"></i>
                    <span class="text-xs">Stacking</span>
                </button>
            </div>
        </div>
    </nav>

    <script src="script.js"></script>
</body>
</html>
