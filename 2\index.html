<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Investment Dashboard</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom Styles -->
    <style>
        /* Define custom styles for the dark theme and glassmorphism effect */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0a0a0a;
            color: #e5e7eb;
        }

        .glass-card {
            background: rgba(22, 22, 30, 0.6);
            /* Slightly darker, less transparent */
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1.5rem;
            /* 24px */
            box-shadow: 0 8px 32px 0 rgba(116, 35, 156, 0.1);
        }

        .gold-gradient-text {
            background: linear-gradient(90deg, #FDBB2D, #D4AF37, #FDBB2D);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }

        .purple-gradient-bg {
            background: linear-gradient(90deg, #8A2BE2, #4B0082);
        }

        .tab-button.active {
            background-color: #4B0082;
            color: #ffffff;
            border-bottom-color: #FDBB2D;
        }

        .table-scrollbar::-webkit-scrollbar {
            width: 4px;
        }

        .table-scrollbar::-webkit-scrollbar-thumb {
            background-color: #4B0082;
            border-radius: 20px;
        }

        .table-scrollbar::-webkit-scrollbar-track {
            background: rgba(17, 24, 39, 0.5);
        }
    </style>
</head>

<body class="bg-black">
    <!-- Main Container -->
    <div class="container mx-auto p-4 sm:p-6 lg:p-8">

        <!-- Header -->
        <header class="mb-8 flex justify-between items-center">
            <h1 class="text-3xl md:text-4xl font-bold gold-gradient-text">Dashboard</h1>
            <div class="flex items-center space-x-2">
                <img src="https://placehold.co/40x40/8A2BE2/FFFFFF?text=A" alt="User Avatar"
                    class="rounded-full border-2 border-purple-500">
                <span class="hidden sm:inline font-medium text-white">Alex Mercer</span>
            </div>
        </header>

        <main class="space-y-8">
            <!-- Main Grid Layout -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column (Main Content) -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Wallet Balance & Actions -->
                    <div class="glass-card p-6">
                        <div class="flex flex-col md:flex-row justify-between md:items-center">
                            <div>
                                <h2 class="text-gray-400 text-sm">Current Balance</h2>
                                <p class="text-4xl lg:text-5xl font-bold text-white">$1,250,780.45</p>
                                <p class="text-green-400 font-semibold mt-1">+$250,780.45 (25.01%) Total Profit</p>
                            </div>
                            <div class="mt-4 md:mt-0">
                                <button
                                    class="w-full md:w-auto purple-gradient-bg text-white font-bold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                                    Deposit
                                </button>
                            </div>
                        </div>
                        <!-- Quick Actions -->
                        <div class="mt-6 pt-6 border-t border-gray-700/50 flex justify-around">
                            <button class="flex flex-col items-center text-gray-300 hover:text-white transition-colors">
                                <div class="p-3 bg-gray-700/60 rounded-full hover:bg-purple-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                                <span class="text-xs mt-2">Add</span>
                            </button>
                            <button class="flex flex-col items-center text-gray-300 hover:text-white transition-colors">
                                <div class="p-3 bg-gray-700/60 rounded-full hover:bg-purple-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5l-3-3m0 0l-3 3m3-3v8">
                                        </path>
                                    </svg>
                                </div>
                                <span class="text-xs mt-2">Withdraw</span>
                            </button>
                            <button class="flex flex-col items-center text-gray-300 hover:text-white transition-colors">
                                <div class="p-3 bg-gray-700/60 rounded-full hover:bg-purple-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </div>
                                <span class="text-xs mt-2">Send</span>
                            </button>
                            <button class="flex flex-col items-center text-gray-300 hover:text-white transition-colors">
                                <div class="p-3 bg-gray-700/60 rounded-full hover:bg-purple-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                    </svg>
                                </div>
                                <span class="text-xs mt-2">Swap</span>
                            </button>
                        </div>
                    </div>

                    <!-- Market Watchlist -->
                    <div class="glass-card p-6">
                        <h3 class="text-xl font-bold text-white mb-4">Market Watchlist</h3>
                        <div class="overflow-x-auto">
                            <table class="w-full text-left">
                                <thead>
                                    <tr class="border-b border-gray-700 text-sm text-gray-400">
                                        <th class="py-3 px-2">Asset</th>
                                        <th class="py-3 px-2">Price</th>
                                        <th class="py-3 px-2">Change (24h)</th>
                                        <th class="py-3 px-2 hidden sm:table-cell">Chart (7d)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-b border-gray-800/50">
                                        <td class="py-4 px-2 flex items-center space-x-3">
                                            <img src="https://i.ibb.co/Yd4Bkb9/btc-logo.png" alt="BTC"
                                                class="w-8 h-8" />
                                            <div>
                                                <p class="font-bold text-white">BTC</p>
                                                <p class="text-xs text-gray-400">Bitcoin</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-2 font-medium text-white">$68,045.50</td>
                                        <td class="py-4 px-2 font-medium text-green-400">*****%</td>
                                        <td class="py-4 px-2 hidden sm:table-cell">
                                            <svg class="w-24 h-8 text-green-500" viewBox="0 0 100 30" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M0 20L10 15L20 18L30 12L40 15L50 10L60 14L70 8L80 12L90 10L100 5"
                                                    stroke="currentColor" stroke-width="2" />
                                            </svg>
                                        </td>
                                    </tr>
                                    <tr class="border-b border-gray-800/50">
                                        <td class="py-4 px-2 flex items-center space-x-3">
                                            <img src="https://i.ibb.co/zHsm3x8/eth-logo.png" alt="ETH"
                                                class="w-8 h-8" />
                                            <div>
                                                <p class="font-bold text-white">ETH</p>
                                                <p class="text-xs text-gray-400">Ethereum</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-2 font-medium text-white">$3,560.80</td>
                                        <td class="py-4 px-2 font-medium text-red-400">-0.55%</td>
                                        <td class="py-4 px-2 hidden sm:table-cell">
                                            <svg class="w-24 h-8 text-red-500" viewBox="0 0 100 30" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M0 10L10 15L20 12L30 18L40 15L50 20L60 16L70 22L80 18L90 20L100 25"
                                                    stroke="currentColor" stroke-width="2" />
                                            </svg>
                                        </td>
                                    </tr>
                                    <tr class="border-b border-gray-800/50">
                                        <td class="py-4 px-2 flex items-center space-x-3">
                                            <img src="https://i.ibb.co/P9Lcrp9/sol-logo.png" alt="SOL"
                                                class="w-8 h-8" />
                                            <div>
                                                <p class="font-bold text-white">SOL</p>
                                                <p class="text-xs text-gray-400">Solana</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-2 font-medium text-white">$168.10</td>
                                        <td class="py-4 px-2 font-medium text-green-400">*****%</td>
                                        <td class="py-4 px-2 hidden sm:table-cell">
                                            <svg class="w-24 h-8 text-green-500" viewBox="0 0 100 30" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M0 25L10 20L20 22L30 15L40 18L50 12L60 16L70 10L80 5L90 8L100 3"
                                                    stroke="currentColor" stroke-width="2" />
                                            </svg>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Right Column (Side Content) -->
                <div class="lg:col-span-1 space-y-8">
                    <!-- User Information Card -->
                    <div class="glass-card p-6 space-y-3">
                        <h3 class="text-xl font-bold text-white mb-3">User Profile</h3>
                        <div class="flex justify-between text-sm"><span class="text-gray-400">User ID:</span> <span
                                class="font-medium text-white">USR-1A7B3C</span></div>
                        <div class="flex justify-between text-sm"><span class="text-gray-400">Register Date:</span>
                            <span class="font-medium text-white">2023-01-15</span></div>
                        <div class="flex justify-between text-sm"><span class="text-gray-400">Last Topup:</span> <span
                                class="font-medium text-white">2024-05-20</span></div>
                        <div class="flex justify-between text-sm"><span class="text-gray-400">Account Type:</span> <span
                                class="font-medium text-yellow-400 bg-yellow-900/50 px-2 py-1 rounded">Trial
                                ($10)</span></div>
                    </div>

                    <!-- Refer Link Section -->
                    <div class="glass-card p-6">
                        <h3 class="text-xl font-bold text-white mb-4">Refer & Earn</h3>
                        <div class="flex flex-col md:flex-row items-center gap-4">
                            <input type="text" readonly value="https://cryptodash.com/ref/USR-1A7B3C"
                                class="w-full bg-gray-900/70 border border-gray-700 rounded-lg px-4 py-3 text-gray-300 focus:outline-none flex-grow">
                        </div>
                        <div class="flex items-center justify-center space-x-3 mt-4">
                            <button
                                class="p-3 bg-gray-700/60 rounded-full hover:bg-purple-600 transition-colors duration-300">
                                <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.894 11.892-1.99 0-3.903-.52-5.586-1.456l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.886-.001 2.267.655 4.398 1.908 6.161l-1.317 4.814 4.893-1.282z" />
                                </svg>
                            </button>
                            <button
                                class="p-3 bg-gray-700/60 rounded-full hover:bg-purple-600 transition-colors duration-300">
                                <svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M9.78 18.65l.28-4.23 7.68-6.92c.34-.31-.07-.46-.52-.19L7.74 13.3 3.65 12c-.88-.25-.88-.86.2-1.3l15.97-6.16c.73-.33 1.43.18 1.15.91L18.23 18.2c-.27.8-.91 1.02-1.51.66l-4.09-3.05-1.97 1.91c-.23.23-.42.42-.83.42z" />
                                </svg>
                            </button>
                            <button
                                class="p-3 bg-gray-700/60 rounded-full hover:bg-purple-600 transition-colors duration-300">
                                <svg class="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12c0 4.84 3.44 8.87 8 9.8V15H8v-3h2V9.5C10 7.57 11.57 6 13.5 6H16v3h-1.5c-1 0-1.5.5-1.5 1.5V12h3l-.5 3h-2.5v6.8c4.56-.93 8-4.96 8-9.8z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transaction & Stacking Sections with Tabs -->
            <div class="glass-card p-4 sm:p-6">
                <!-- Tab Buttons -->
                <div class="mb-6 border-b border-gray-700 flex flex-wrap">
                    <button
                        class="tab-button active border-b-2 text-sm sm:text-base font-medium py-3 px-4 text-gray-300 hover:text-white transition-colors duration-300"
                        data-tab="deposit">Deposit</button>
                    <button
                        class="tab-button border-b-2 border-transparent text-sm sm:text-base font-medium py-3 px-4 text-gray-300 hover:text-white transition-colors duration-300"
                        data-tab="topup">Top-up Account</button>
                    <button
                        class="tab-button border-b-2 border-transparent text-sm sm:text-base font-medium py-3 px-4 text-gray-300 hover:text-white transition-colors duration-300"
                        data-tab="transfer">Transfer</button>
                    <button
                        class="tab-button border-b-2 border-transparent text-sm sm:text-base font-medium py-3 px-4 text-gray-300 hover:text-white transition-colors duration-300"
                        data-tab="stacking">Stacking BTC</button>
                </div>

                <!-- Tab Content -->
                <div id="tab-content">
                    <!-- Deposit Panel -->
                    <div id="deposit" class="tab-panel space-y-6">
                        <form class="space-y-4">
                            <input type="number" placeholder="Amount (USD)"
                                class="w-full bg-gray-900/70 border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <button type="submit"
                                class="w-full purple-gradient-bg text-white font-bold py-3 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">Deposit
                                Now</button>
                        </form>
                        <div class="transaction-report">
                            <h4 class="font-semibold text-white mb-2">Deposit History</h4>
                            <div class="overflow-y-auto max-h-48 table-scrollbar pr-2">
                                <table class="w-full text-sm text-left">
                                    <thead class="text-xs text-gray-400 uppercase">
                                        <tr>
                                            <th class="py-2 px-4">Date</th>
                                            <th class="py-2 px-4">Amount</th>
                                            <th class="py-2 px-4">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-gray-200">
                                        <tr class="border-b border-gray-800">
                                            <td class="py-2 px-4">2024-05-20</td>
                                            <td class="py-2 px-4">$500.00</td>
                                            <td class="py-2 px-4 text-green-400">Completed</td>
                                        </tr>
                                        <tr class="border-b border-gray-800">
                                            <td class="py-2 px-4">2024-04-11</td>
                                            <td class="py-2 px-4">$250.00</td>
                                            <td class="py-2 px-4 text-green-400">Completed</td>
                                        </tr>
                                        <tr class="border-b border-gray-800">
                                            <td class="py-2 px-4">2024-03-01</td>
                                            <td class="py-2 px-4">$1000.00</td>
                                            <td class="py-2 px-4 text-green-400">Completed</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Top-up Panel -->
                    <div id="topup" class="tab-panel hidden space-y-6">
                        <form class="space-y-4">
                            <input type="number" placeholder="Amount (USD)"
                                class="w-full bg-gray-900/70 border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <button type="submit"
                                class="w-full purple-gradient-bg text-white font-bold py-3 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">Top-up
                                Account</button>
                        </form>
                        <div class="transaction-report">
                            <h4 class="font-semibold text-white mb-2">Top-up History</h4>
                            <p class="text-gray-400">No top-up history found.</p>
                        </div>
                    </div>

                    <!-- Transfer Panel -->
                    <div id="transfer" class="tab-panel hidden space-y-6">
                        <form class="space-y-4">
                            <input type="text" placeholder="Recipient Wallet Address"
                                class="w-full bg-gray-900/70 border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <input type="number" placeholder="Amount (BTC)"
                                class="w-full bg-gray-900/70 border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <button type="submit"
                                class="w-full purple-gradient-bg text-white font-bold py-3 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">Transfer
                                Crypto</button>
                        </form>
                        <div class="transaction-report">
                            <h4 class="font-semibold text-white mb-2">Transfer History</h4>
                            <div class="overflow-y-auto max-h-48 table-scrollbar pr-2">
                                <table class="w-full text-sm text-left">
                                    <thead class="text-xs text-gray-400 uppercase">
                                        <tr>
                                            <th class="py-2 px-4">Date</th>
                                            <th class="py-2 px-4">Amount</th>
                                            <th class="py-2 px-4">To</th>
                                            <th class="py-2 px-4">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-gray-200">
                                        <tr class="border-b border-gray-800">
                                            <td class="py-2 px-4">2024-05-18</td>
                                            <td class="py-2 px-4">0.5 BTC</td>
                                            <td class="py-2 px-4">...a4b8</td>
                                            <td class="py-2 px-4 text-green-400">Completed</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Stacking Panel -->
                    <div id="stacking" class="tab-panel hidden space-y-6">
                        <div class="bg-purple-900/30 border-l-4 border-purple-400 p-4 rounded-r-lg">
                            <p class="text-purple-200 text-sm">Stacking coin time is a minimum of 6 months. You can
                                withdraw your staked assets and profits after this period.</p>
                        </div>
                        <form class="space-y-4">
                            <input type="number" placeholder="Amount (BTC to Stack)"
                                class="w-full bg-gray-900/70 border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <button type="submit"
                                class="w-full purple-gradient-bg text-white font-bold py-3 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">Stack
                                BTC Now</button>
                        </form>
                        <div class="transaction-report">
                            <h4 class="font-semibold text-white mb-2">Stacking History</h4>
                            <div class="overflow-y-auto max-h-48 table-scrollbar pr-2">
                                <table class="w-full text-sm text-left">
                                    <thead class="text-xs text-gray-400 uppercase">
                                        <tr>
                                            <th class="py-2 px-4">Date</th>
                                            <th class="py-2 px-4">Amount</th>
                                            <th class="py-2 px-4">Unlocks On</th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-gray-200">
                                        <tr class="border-b border-gray-800">
                                            <td class="py-2 px-4">2024-02-01</td>
                                            <td class="py-2 px-4">10 BTC</td>
                                            <td class="py-2 px-4">2024-08-01</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </main>

        <!-- Footer for safe area padding on mobile -->
        <footer class="pt-8">
            <p class="text-center text-xs text-gray-600">&copy; 2024 CryptoDash. All rights reserved.</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanels = document.querySelectorAll('.tab-panel');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Deactivate all buttons and hide all panels
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.add('border-transparent');
                    });
                    tabPanels.forEach(panel => panel.classList.add('hidden'));

                    // Activate the clicked button
                    button.classList.add('active');
                    button.classList.remove('border-transparent');


                    // Show the corresponding panel
                    const tabName = button.getAttribute('data-tab');
                    const targetPanel = document.getElementById(tabName);
                    if (targetPanel) {
                        targetPanel.classList.remove('hidden');
                    }
                });
            });
        });
    </script>
</body>

</html>