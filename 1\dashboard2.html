<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Dashboard - Oceanic Theme</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        @property --angle {
          syntax: '<angle>';
          initial-value: 0deg;
          inherits: false;
        }

        @keyframes rotate-border {
          to {
            --angle: 360deg;
          }
        }

        .animated-border-card {
            --angle: 0deg;
            position: relative;
            border: 3px solid;
            border-image: conic-gradient(from var(--angle), var(--c1), var(--c2), var(--c3), var(--c2), var(--c1)) 1;
            animation: rotate-border 5s linear infinite;
        }

        .ocean-theme {
            --c1: #2dd4bf; /* teal-400 */
            --c2: #3b82f6; /* blue-500 */
            --c3: #67e8f9; /* cyan-300 */
        }

        .glass-card-inner {
            background: rgba(255, 255, 255, 0.05);
            -webkit-backdrop-filter: blur(12px);
            backdrop-filter: blur(12px);
        }
        
        body {
            background-image: url('https://www.transparenttextures.com/patterns/cubes.png');
        }

        #sidebar {
            transition: transform 0.3s ease-in-out;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-cyan-900 to-blue-900 text-white font-sans min-h-screen">

    <div class="relative min-h-screen md:flex">
        <!-- Mobile Nav Button -->
        <div class="md:hidden flex justify-between items-center p-4">
            <h1 class="text-2xl font-bold text-teal-300">MyCrypto</h1>
            <button id="menu-btn" class="text-white focus:outline-none">
                <i class="fas fa-bars text-2xl"></i>
            </button>
        </div>

        <!-- Sidebar -->
        <aside id="sidebar" class="w-64 h-screen glass-card-inner p-6 flex-col justify-between fixed inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 z-30 md:flex border-r border-cyan-500/30">
            <div>
                <h1 class="text-2xl font-bold text-teal-300 mb-10 hidden md:block">MyCrypto</h1>
                <nav class="space-y-4">
                    <a href="#" class="flex items-center space-x-3 text-teal-200 hover:text-white bg-cyan-800/50 p-3 rounded-lg">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="#" class="flex items-center space-x-3 text-teal-200 hover:text-white">
                        <i class="fas fa-wallet"></i>
                        <span>Wallet</span>
                    </a>
                    <a href="#" class="flex items-center space-x-3 text-teal-200 hover:text-white">
                        <i class="fas fa-exchange-alt"></i>
                        <span>Transactions</span>
                    </a>
                    <a href="#" class="flex items-center space-x-3 text-teal-200 hover:text-white">
                        <i class="fas fa-chart-line"></i>
                        <span>Markets</span>
                    </a>
                </nav>
            </div>
            <div class="space-y-2">
                 <a href="#" class="flex items-center space-x-3 text-teal-200 hover:text-white">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
                <a href="#" class="flex items-center space-x-3 text-teal-200 hover:text-white">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-4 md:p-10">
            <header class="hidden md:flex justify-between items-center mb-10">
                <h2 class="text-3xl font-bold text-teal-300">Dashboard</h2>
                <button class="bg-gradient-to-r from-blue-600 to-teal-500 hover:from-blue-700 hover:to-teal-600 text-white font-bold py-3 px-6 rounded-lg transition-all flex items-center space-x-2">
                    <i class="fas fa-wallet"></i>
                    <span>Connect Wallet</span>
                </button>
            </header>

            <!-- Grid Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Balance Card -->
                <div class="animated-border-card ocean-theme rounded-xl">
                    <div class="glass-card-inner rounded-lg p-6 h-full w-full">
                        <h3 class="font-semibold text-lg mb-3 text-teal-300">Total Balance</h3>
                        <p class="text-3xl md:text-4xl font-bold text-white">1.25 ETH</p>
                        <p class="text-cyan-200 mt-2">$2,912.50 USD</p>
                    </div>
                </div>

                <!-- Profit Card -->
                <div class="animated-border-card ocean-theme rounded-xl">
                    <div class="glass-card-inner rounded-lg p-6 h-full w-full">
                        <h3 class="font-semibold text-lg mb-3 text-teal-300">24h Profit</h3>
                        <p class="text-3xl md:text-4xl font-bold text-green-400">+$158.42</p>
                        <p class="text-cyan-200 mt-2">+5.8%</p>
                    </div>
                </div>
                
                <!-- Market Cap Card -->
                <div class="animated-border-card ocean-theme rounded-xl">
                    <div class="glass-card-inner rounded-lg p-6 h-full w-full">
                        <h3 class="font-semibold text-lg mb-3 text-teal-300">ETH Market Cap</h3>
                        <p class="text-3xl md:text-4xl font-bold text-white">$349.8B</p>
                        <p class="text-cyan-200 mt-2">#2 Ranked</p>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="mt-10 animated-border-card ocean-theme rounded-xl">
                <div class="glass-card-inner rounded-lg p-4 md:p-6 overflow-x-auto">
                    <h3 class="font-semibold text-xl mb-4 text-teal-300">Recent Activity</h3>
                    <table class="w-full text-left min-w-max">
                        <thead>
                            <tr class="border-b border-cyan-800">
                                <th class="py-3 px-2">Type</th>
                                <th class="py-3 px-2">Amount</th>
                                <th class="py-3 px-2">Status</th>
                                <th class="py-3 px-2">Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-cyan-900/50">
                                <td class="py-4 px-2 text-teal-200">Sent</td>
                                <td class="py-4 px-2 font-mono">0.05 ETH</td>
                                <td class="py-4 px-2 text-green-400">Completed</td>
                                <td class="py-4 px-2 text-cyan-400">2025-07-03</td>
                            </tr>
                            <tr class="border-b border-cyan-900/50">
                                <td class="py-4 px-2 text-teal-200">Received</td>
                                <td class="py-4 px-2 font-mono">0.20 ETH</td>
                                <td class="py-4 px-2 text-green-400">Completed</td>
                                <td class="py-4 px-2 text-cyan-400">2025-07-02</td>
                            </tr>
                            <tr>
                                <td class="py-4 px-2 text-teal-200">Swapped</td>
                                <td class="py-4 px-2 font-mono">150 USDC</td>
                                <td class="py-4 px-2 text-yellow-400">Pending</td>
                                <td class="py-4 px-2 text-cyan-400">2025-07-03</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <script>
        const menuBtn = document.getElementById('menu-btn');
        const sidebar = document.getElementById('sidebar');

        menuBtn.addEventListener('click', () => {
            sidebar.classList.toggle('-translate-x-full');
        });
    </script>

</body>
</html>