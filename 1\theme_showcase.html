<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto UI Theme Showcase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        @property --angle {
          syntax: '<angle>';
          initial-value: 0deg;
          inherits: false;
        }

        @keyframes rotate-border {
          to {
            --angle: 360deg;
          }
        }

        .animated-border-card {
            --angle: 0deg;
            position: relative;
            padding: 3px;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .animated-border-card::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200%;
            height: 200%;
            background: conic-gradient(
                from var(--angle), 
                var(--c1, #8b5cf6), 
                var(--c2, #6366f1), 
                var(--c3, #a78bfa), 
                var(--c2, #6366f1), 
                var(--c1, #8b5cf6)
            );
            animation: rotate-border 4s linear infinite;
            z-index: -1;
        }

        .animated-border-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }
        
        .eth-theme { --c1: #8b5cf6; --c2: #6366f1; --c3: #a78bfa; }
        .ocean-theme { --c1: #2dd4bf; --c2: #3b82f6; --c3: #67e8f9; }
        .btc-theme { --c1: #f59e0b; --c2: #f97316; --c3: #fcd34d; }
        .silver-theme { --c1: #94a3b8; --c2: #64748b; --c3: #cbd5e1; }

        .glass-card-inner {
            background-color: #0F172A; /* Opaque background to hide animation */
            /* Simulated glass effect */
            background-image: radial-gradient(circle at 50% 0%, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #0F172A;
            background-image: radial-gradient(circle at top right, rgba(121, 68, 255, 0.1), transparent 50%), 
                              radial-gradient(circle at bottom left, rgba(30, 144, 255, 0.1), transparent 50%);
            min-height: 100vh;
        }
        
        .toggle-checkbox:checked {
            right: 0;
            border-color: #fff;
            transform: translateX(100%);
        }
        .toggle-checkbox {
            transition: all 0.2s ease-in-out;
            transform: translateX(0);
        }
        
        button {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

    </style>
</head>
<body class="bg-gray-900 text-white p-4 md:p-8">

    <h1 class="text-4xl md:text-5xl font-bold mb-16 text-center tracking-wider text-slate-200">Crypto UI Theme Showcase</h1>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-10 md:gap-16 max-w-7xl mx-auto">

        <!-- Ethereum Card -->
        <div class="animated-border-card eth-theme rounded-2xl">
            <div class="h-full w-full rounded-xl p-6 md:p-8 bg-gradient-to-br from-indigo-900/80 to-purple-900/80 glass-card-inner">
                <h2 class="text-2xl font-bold mb-6 border-b-2 pb-3 border-purple-400/50 text-purple-300 flex items-center gap-3"><i class="fab fa-ethereum"></i> Ethereum Theme</h2>
                <div class="space-y-6">
                    <button class="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold py-3 px-4 rounded-lg text-lg">
                        <i class="fas fa-wallet mr-2"></i> Connect Wallet
                    </button>
                    <input type="text" placeholder="Enter amount in ETH..." class="w-full p-3 rounded-lg bg-purple-900/70 border border-purple-700/50 focus:ring-2 focus:ring-purple-500 focus:outline-none placeholder-purple-300/70">
                    
                    <div class="text-sm text-purple-300">
                        <div class="flex justify-between mb-1">
                            <span>Transaction Progress</span>
                            <span>65%</span>
                        </div>
                        <div class="w-full bg-purple-900/70 rounded-full h-2.5">
                            <div class="bg-gradient-to-r from-purple-500 to-indigo-500 h-2.5 rounded-full" style="width: 65%"></div>
                        </div>
                    </div>

                    <div class="bg-purple-900/70 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-200 mb-3">Recent Activity</h4>
                        <ul class="space-y-3 text-sm">
                            <li class="flex justify-between items-center">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-arrow-up text-red-400"></i>
                                    <span>Sent 0.5 ETH</span>
                                </div>
                                <span class="font-mono text-xs text-purple-400">3m ago</span>
                            </li>
                            <li class="flex justify-between items-center">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-random text-blue-400"></i>
                                    <span>Swapped for DAI</span>
                                </div>
                                <span class="font-mono text-xs text-purple-400">1h ago</span>
                            </li>
                        </ul>
                    </div>

                    <div class="flex items-center justify-between bg-purple-900/70 p-3 rounded-lg">
                        <span class="text-purple-200 font-semibold">Enable Expert Mode</span>
                        <div class="relative inline-block w-12 mr-2 align-middle select-none">
                            <input type="checkbox" name="toggle" id="eth-toggle" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 border-purple-800 appearance-none cursor-pointer"/>
                            <label for="eth-toggle" class="toggle-label block overflow-hidden h-6 rounded-full bg-purple-800 cursor-pointer"></label>
                        </div>
                    </div>
                    <div class="bg-green-500/20 border border-green-400/50 text-green-300 px-4 py-3 rounded-lg flex items-center gap-3" role="alert">
                      <i class="fas fa-check-circle"></i>
                      <div>
                        <strong class="font-bold">Success!</strong>
                        <span class="block sm:inline">Your transaction has been confirmed.</span>
                      </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Oceanic Card -->
        <div class="animated-border-card ocean-theme rounded-2xl">
            <div class="h-full w-full rounded-xl p-6 md:p-8 bg-gradient-to-br from-cyan-900/80 to-blue-900/80 glass-card-inner">
                <h2 class="text-2xl font-bold mb-6 border-b-2 pb-3 border-teal-400/50 text-teal-300 flex items-center gap-3"><i class="fas fa-water"></i> Oceanic Theme</h2>
                <div class="space-y-6">
                    <button class="w-full bg-gradient-to-r from-blue-600 to-teal-500 hover:from-blue-700 hover:to-teal-600 text-white font-bold py-3 px-4 rounded-lg text-lg">
                        <i class="fas fa-exchange-alt mr-2"></i> Trade Now
                    </button>
                    <input type="text" placeholder="Search for a token..." class="w-full p-3 rounded-lg bg-cyan-900/70 border border-cyan-700/50 focus:ring-2 focus:ring-teal-500 focus:outline-none placeholder-cyan-300/70">

                    <div>
                        <div class="flex justify-between text-sm text-cyan-200">
                            <label for="slippage" class="font-semibold">Slippage Tolerance</label>
                            <span>1.5%</span>
                        </div>
                        <input id="slippage" type="range" min="0" max="5" value="1.5" step="0.1" class="w-full h-2 bg-cyan-800/80 rounded-lg appearance-none cursor-pointer accent-teal-400">
                    </div>

                    <div>
                        <div class="border-b border-cyan-700/50">
                            <nav class="-mb-px flex space-x-6" aria-label="Tabs">
                                <a href="#" class="border-teal-400 text-teal-300 whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm">Swap</a>
                                <a href="#" class="border-transparent text-cyan-400 hover:text-teal-300 hover:border-cyan-500/50 whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm">Pool</a>
                                <a href="#" class="border-transparent text-cyan-400 hover:text-teal-300 hover:border-cyan-500/50 whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm">Vote</a>
                            </nav>
                        </div>
                    </div>

                    <div class="flex items-center justify-between bg-cyan-900/70 p-3 rounded-lg">
                        <span class="text-cyan-200 font-semibold">Auto-slippage</span>
                        <div class="relative inline-block w-12 mr-2 align-middle select-none">
                            <input type="checkbox" name="toggle" id="ocean-toggle" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 border-cyan-800 appearance-none cursor-pointer"/>
                            <label for="ocean-toggle" class="toggle-label block overflow-hidden h-6 rounded-full bg-cyan-800 cursor-pointer"></label>
                        </div>
                    </div>
                    <div class="bg-yellow-500/20 border border-yellow-400/50 text-yellow-300 px-4 py-3 rounded-lg flex items-center gap-3" role="alert">
                      <i class="fas fa-exclamation-triangle"></i>
                      <div>
                        <strong class="font-bold">Warning!</strong>
                        <span class="block sm:inline">High price volatility detected.</span>
                      </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bitcoin Card -->
        <div class="animated-border-card btc-theme rounded-2xl">
            <div class="h-full w-full rounded-xl p-6 md:p-8 bg-gradient-to-br from-gray-800/80 to-orange-900/80 glass-card-inner">
                <h2 class="text-2xl font-bold mb-6 border-b-2 pb-3 border-yellow-400/50 text-yellow-300 flex items-center gap-3"><i class="fab fa-btc"></i> Bitcoin Theme</h2>
                <div class="space-y-6">
                    <button class="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-gray-900 font-bold py-3 px-4 rounded-lg text-lg">
                        <i class="fas fa-arrow-down mr-2"></i> Deposit BTC
                    </button>
                    <input type="text" placeholder="Enter BTC address..." class="w-full p-3 rounded-lg bg-gray-900/70 border border-yellow-700/50 focus:ring-2 focus:ring-yellow-500 focus:outline-none placeholder-yellow-300/70">
                    
                    <div class="relative">
                        <select class="w-full p-3 rounded-lg bg-gray-900/70 border border-yellow-700/50 focus:ring-2 focus:ring-yellow-500 focus:outline-none appearance-none text-yellow-200">
                            <option>Bitcoin Mainnet</option>
                            <option>Lightning Network</option>
                            <option>Testnet</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-yellow-300">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>

                    <div class="flex items-center gap-4 bg-gray-900/70 p-3 rounded-lg">
                        <img class="h-12 w-12 rounded-full" src="https://i.pravatar.cc/150?u=satoshi" alt="User Avatar">
                        <div>
                            <p class="font-semibold text-yellow-200">Satoshi N.</p>
                            <p class="text-xs text-yellow-400/80">Tier 3 Verified</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between bg-gray-900/70 p-3 rounded-lg">
                        <span class="text-yellow-200 font-semibold">Save Address</span>
                        <div class="relative inline-block w-12 mr-2 align-middle select-none">
                            <input type="checkbox" name="toggle" id="btc-toggle" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 border-gray-700 appearance-none cursor-pointer"/>
                            <label for="btc-toggle" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-700 cursor-pointer"></label>
                        </div>
                    </div>
                    <div class="bg-blue-500/20 border border-blue-400/50 text-blue-300 px-4 py-3 rounded-lg flex items-center gap-3" role="alert">
                      <i class="fas fa-info-circle"></i>
                      <div>
                        <strong class="font-bold">Info:</strong>
                        <span class="block sm:inline">Your deposit may take up to 30 mins.</span>
                      </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Silver Card -->
        <div class="animated-border-card silver-theme rounded-2xl">
            <div class="h-full w-full rounded-xl p-6 md:p-8 bg-gradient-to-br from-slate-800/80 to-gray-800/80 glass-card-inner">
                <h2 class="text-2xl font-bold mb-6 border-b-2 pb-3 border-slate-400/50 text-slate-300 flex items-center gap-3"><i class="fas fa-shield-alt"></i> Silver Theme</h2>
                <div class="space-y-6">
                    <div class="flex gap-4">
                        <button class="w-full bg-gradient-to-r from-slate-500 to-gray-500 hover:from-slate-600 hover:to-gray-600 text-white font-bold py-3 px-4 rounded-lg text-lg">
                            <i class="fas fa-receipt mr-2"></i> View
                        </button>
                        <button class="w-full bg-transparent border-2 border-slate-600 hover:bg-slate-700/50 text-slate-300 font-bold py-3 px-4 rounded-lg text-lg">
                            <i class="fas fa-file-export mr-2"></i> Export
                        </button>
                    </div>
                    <input type="text" placeholder="Search by Txn Hash..." class="w-full p-3 rounded-lg bg-slate-900/70 border border-slate-700/50 focus:ring-2 focus:ring-slate-500 focus:outline-none placeholder-slate-300/70">
                    
                    <div class="overflow-x-auto bg-slate-900/70 rounded-lg p-1">
                        <table class="w-full text-sm text-left text-slate-300">
                            <thead class="text-xs text-slate-400 uppercase bg-slate-800/50">
                                <tr>
                                    <th scope="col" class="px-4 py-3">Asset</th>
                                    <th scope="col" class="px-4 py-3">Status</th>
                                    <th scope="col" class="px-4 py-3 text-right">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-slate-700/50">
                                    <th scope="row" class="px-4 py-3 font-medium whitespace-nowrap">Monero (XMR)</th>
                                    <td class="px-4 py-3"><span class="bg-green-500/20 text-green-300 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">Complete</span></td>
                                    <td class="px-4 py-3 text-right font-mono">+5.2</td>
                                </tr>
                                <tr class="border-b border-slate-700/50">
                                    <th scope="row" class="px-4 py-3 font-medium whitespace-nowrap">Zcash (ZEC)</th>
                                    <td class="px-4 py-3"><span class="bg-red-500/20 text-red-300 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">Failed</span></td>
                                    <td class="px-4 py-3 text-right font-mono">-1.0</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="flex items-center justify-between bg-slate-900/70 p-3 rounded-lg">
                        <span class="text-slate-200 font-semibold">Filter Unknown</span>
                        <div class="relative inline-block w-12 mr-2 align-middle select-none">
                            <input type="checkbox" name="toggle" id="silver-toggle" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 border-slate-700 appearance-none cursor-pointer"/>
                            <label for="silver-toggle" class="toggle-label block overflow-hidden h-6 rounded-full bg-slate-700 cursor-pointer"></label>
                        </div>
                    </div>
                    <div class="bg-red-500/20 border border-red-400/50 text-red-300 px-4 py-3 rounded-lg flex items-center gap-3" role="alert">
                      <i class="fas fa-times-circle"></i>
                      <div>
                        <strong class="font-bold">Error!</strong>
                        <span class="block sm:inline">Failed to fetch transaction details.</span>
                      </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        function startCountdown(duration, display) {
            let timer = duration, days, hours, minutes, seconds;
            setInterval(function () {
                days = parseInt(timer / (60 * 60 * 24), 10);
                hours = parseInt((timer % (60 * 60 * 24)) / (60 * 60), 10);
                minutes = parseInt((timer % (60 * 60)) / 60, 10);
                seconds = parseInt(timer % 60, 10);

                days = days < 10 ? "0" + days : days;
                hours = hours < 10 ? "0" + hours : hours;
                minutes = minutes < 10 ? "0" + minutes : minutes;
                seconds = seconds < 10 ? "0" + seconds : seconds;

                display.querySelector("#days").textContent = days;
                display.querySelector("#hours").textContent = hours;
                display.querySelector("#minutes").textContent = minutes;
                display.querySelector("#seconds").textContent = seconds;

                if (--timer < 0) {
                    timer = duration;
                }
            }, 1000);
        }

        window.onload = function () {
            const twoWeeks = 14 * 24 * 60 * 60;
            const display = document.querySelector('#countdown');
            if (display) {
                startCountdown(twoWeeks, display);
            }
        };
    </script>

</body>
</html>