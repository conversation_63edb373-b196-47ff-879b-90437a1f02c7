<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto UI Palettes with Glassmorphism</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            -webkit-backdrop-filter: blur(12px);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .palette-card {
            @apply p-6 rounded-2xl shadow-lg transition-all duration-300;
        }
        .palette-title {
            @apply text-2xl font-bold mb-6 border-b-2 pb-3;
        }
        .color-swatch {
            @apply p-4 rounded-lg text-center font-semibold shadow-md;
        }
        .demo-card {
            @apply rounded-lg p-4 shadow-inner;
        }
        body {
            background-image: url('https://www.transparenttextures.com/patterns/cubes.png');
        }
    </style>
</head>
<body class="bg-gray-900 text-white p-8 font-sans">

    <h1 class="text-4xl font-bold mb-12 text-center tracking-wider">Crypto UI Palettes</h1>

    <div class="space-y-16">

        <!-- Ethereum (Purple) Palette -->
        <div class="palette-card bg-gradient-to-br from-indigo-900 to-purple-900">
            <h2 class="palette-title border-purple-400 text-purple-300">Ethereum Palette</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="font-semibold text-lg mb-3 text-purple-300">Color Swatches</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="color-swatch bg-purple-700">Primary</div>
                        <div class="color-swatch bg-purple-500">Accent</div>
                        <div class="color-swatch bg-indigo-900 border border-purple-700">Background</div>
                        <div class="color-swatch text-gray-900 bg-purple-200">Text/Light</div>
                    </div>
                </div>
                <div>
                    <h3 class="font-semibold text-lg mb-3 text-purple-300">UI Elements (Glassmorphism)</h3>
                    <div class="demo-card glass-card space-y-4 border-purple-500/50">
                        <button class="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold py-2 px-4 rounded-lg transition-all">Connect Wallet</button>
                        <div class="bg-purple-900/50 p-3 rounded-lg">
                            <p class="text-purple-200">Balance: <span class="font-bold text-white">1.25 ETH</span></p>
                        </div>
                        <p class="text-sm text-purple-300">A subtle notification message for the user.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Blue & Teal Palette -->
        <div class="palette-card bg-gradient-to-br from-cyan-900 to-blue-900">
            <h2 class="palette-title border-teal-400 text-teal-300">Oceanic Palette</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="font-semibold text-lg mb-3 text-teal-300">Color Swatches</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="color-swatch bg-blue-600">Primary</div>
                        <div class="color-swatch bg-teal-400 text-gray-900">Accent</div>
                        <div class="color-swatch bg-cyan-900 border border-blue-700">Background</div>
                        <div class="color-swatch text-gray-900 bg-blue-100">Text/Light</div>
                    </div>
                </div>
                <div>
                    <h3 class="font-semibold text-lg mb-3 text-teal-300">UI Elements (Glassmorphism)</h3>
                    <div class="demo-card glass-card space-y-4 border-teal-500/50">
                        <button class="w-full bg-gradient-to-r from-blue-600 to-teal-500 hover:from-blue-700 hover:to-teal-600 text-white font-bold py-2 px-4 rounded-lg transition-all">Trade Now</button>
                        <div class="bg-teal-400/20 p-3 rounded-lg">
                            <p class="text-teal-200">Market is <span class="font-bold text-white">bullish</span></p>
                        </div>
                        <p class="text-sm text-cyan-300">A subtle notification message for the user.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bitcoin (Golden) Palette -->
        <div class="palette-card bg-gradient-to-br from-gray-900 to-orange-900">
            <h2 class="palette-title border-yellow-400 text-yellow-300">Bitcoin Palette</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="font-semibold text-lg mb-3 text-yellow-300">Color Swatches</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="color-swatch bg-yellow-500 text-gray-900">Primary</div>
                        <div class="color-swatch bg-orange-500 text-white">Accent</div>
                        <div class="color-swatch bg-gray-800 border border-yellow-700">Background</div>
                        <div class="color-swatch text-gray-900 bg-yellow-100">Text/Light</div>
                    </div>
                </div>
                <div>
                    <h3 class="font-semibold text-lg mb-3 text-yellow-300">UI Elements (Glassmorphism)</h3>
                    <div class="demo-card glass-card space-y-4 border-yellow-500/50">
                        <button class="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-gray-900 font-bold py-2 px-4 rounded-lg transition-all">Deposit BTC</button>
                        <div class="bg-orange-500/20 p-3 rounded-lg">
                            <p class="text-orange-200">Network Fee: <span class="font-bold text-white">0.0001 BTC</span></p>
                        </div>
                        <p class="text-sm text-yellow-300">A subtle notification message for the user.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Silver Palette -->
        <div class="palette-card bg-gradient-to-br from-slate-800 to-gray-800">
            <h2 class="palette-title border-slate-400 text-slate-300">Silver Standard Palette</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="font-semibold text-lg mb-3 text-slate-300">Color Swatches</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="color-swatch bg-slate-500">Primary</div>
                        <div class="color-swatch bg-slate-300 text-gray-900">Accent</div>
                        <div class="color-swatch bg-slate-800 border border-slate-600">Background</div>
                        <div class="color-swatch text-gray-900 bg-slate-100">Text/Light</div>
                    </div>
                </div>
                <div>
                    <h3 class="font-semibold text-lg mb-3 text-slate-300">UI Elements (Glassmorphism)</h3>
                    <div class="demo-card glass-card space-y-4 border-slate-500/50">
                        <button class="w-full bg-gradient-to-r from-slate-500 to-gray-500 hover:from-slate-600 hover:to-gray-600 text-white font-bold py-2 px-4 rounded-lg transition-all">View Transaction</button>
                        <div class="bg-slate-300/20 p-3 rounded-lg">
                            <p class="text-slate-200">Status: <span class="font-bold text-white">Confirmed</span></p>
                        </div>
                        <p class="text-sm text-slate-300">A subtle notification message for the user.</p>
                    </div>
                </div>
            </div>
        </div>

    </div>

</body>
</html>