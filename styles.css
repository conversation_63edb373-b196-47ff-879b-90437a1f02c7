/* Additional Premium Crypto UI Styles */

/* Enhanced Glassmorphism Effects */
.glassmorphism {
    background: rgba(26, 26, 26, 0.85);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Premium Card Hover Effects */
.hover-glow {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.hover-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    transition: left 0.6s;
}

.hover-glow:hover::before {
    left: 100%;
}

.hover-glow:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.15), 0 0 30px rgba(107, 70, 193, 0.1);
    border-color: rgba(255, 215, 0, 0.2);
}

/* Enhanced Coin Animation */
.coin-pulse {
    animation: coinFloat 3s ease-in-out infinite;
    position: relative;
}

@keyframes coinFloat {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg);
        filter: drop-shadow(0 4px 8px rgba(255, 215, 0, 0.3));
    }
    50% { 
        transform: translateY(-8px) rotate(180deg);
        filter: drop-shadow(0 8px 16px rgba(255, 215, 0, 0.5));
    }
}

/* Premium Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 50%, #ff8c00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(20deg); }
}

/* Enhanced Button Styles */
.nav-btn {
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.nav-btn:hover::before {
    width: 100px;
    height: 100px;
}

/* Form Input Enhancements */
input[type="number"], input[type="text"], select, textarea {
    transition: all 0.3s ease;
    position: relative;
}

input[type="number"]:focus, input[type="text"]:focus, select:focus, textarea:focus {
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
    transform: translateY(-1px);
}

/* Status Badge Animations */
.status-badge {
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Card Loading Animation */
.card-loading {
    background: linear-gradient(90deg, rgba(26, 26, 26, 0.8) 25%, rgba(45, 45, 45, 0.8) 50%, rgba(26, 26, 26, 0.8) 75%);
    background-size: 200% 100%;
    animation: cardShimmer 1.5s infinite;
}

@keyframes cardShimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .glassmorphism {
        backdrop-filter: blur(10px);
        background: rgba(26, 26, 26, 0.9);
    }
    
    .hover-glow:hover {
        transform: translateY(-2px);
    }
    
    .coin-pulse {
        animation-duration: 2s;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(26, 26, 26, 0.5);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ffb347, #ffd700);
}

/* Transaction History Enhancements */
.transaction-item {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.transaction-item:hover {
    border-left-color: #ffd700;
    background: rgba(255, 215, 0, 0.05);
    transform: translateX(4px);
}

/* Premium Loading Spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 215, 0, 0.3);
    border-top: 3px solid #ffd700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Message Styles */
.message-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.message-error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

/* Chart/Graph Placeholder Styles */
.chart-placeholder {
    background: linear-gradient(135deg, rgba(107, 70, 193, 0.1), rgba(255, 215, 0, 0.1));
    border: 1px dashed rgba(255, 215, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.chart-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    animation: chartShimmer 2s infinite;
}

@keyframes chartShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}
