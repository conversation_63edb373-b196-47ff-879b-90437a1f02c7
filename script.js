// Global state management
let currentSection = "dashboard";
let transactions = {
  deposits: [
    {
      id: 1,
      amount: 100,
      date: "2024-06-20",
      status: "Completed",
      method: "Bank Transfer",
    },
    {
      id: 2,
      amount: 50,
      date: "2024-06-15",
      status: "Completed",
      method: "Credit Card",
    },
  ],
  topups: [
    {
      id: 1,
      amount: 25,
      date: "2024-06-18",
      status: "Completed",
      type: "Account Balance",
    },
  ],
  transfers: [
    {
      id: 1,
      amount: 75,
      date: "2024-06-19",
      status: "Completed",
      to: "External Wallet",
    },
  ],
  stacking: [
    {
      id: 1,
      amount: 10,
      coin: "BTC",
      date: "2024-06-10",
      status: "Active",
      duration: "6 months",
    },
  ],
};

// Initialize app
document.addEventListener("DOMContentLoaded", function () {
  createSections();
  showSection("dashboard");
});

// Create all sections dynamically
function createSections() {
  const main = document.querySelector("main");

  // Refer Link Section
  const referSection = createReferSection();
  main.appendChild(referSection);

  // Deposit Section
  const depositSection = createDepositSection();
  main.appendChild(depositSection);

  // Top-up Section
  const topupSection = createTopupSection();
  main.appendChild(topupSection);

  // Transfer Section
  const transferSection = createTransferSection();
  main.appendChild(transferSection);

  // Stacking Section
  const stackingSection = createStackingSection();
  main.appendChild(stackingSection);
}

// Create Refer Link Section
function createReferSection() {
  const section = document.createElement("div");
  section.id = "refer-section";
  section.className = "hidden";
  section.innerHTML = `
        <div class="container mx-auto space-y-6">
            <div class="glassmorphism p-6 rounded-lg">
                <h2 class="text-2xl font-bold mb-6 gradient-text">🔗 Referral Program</h2>
                
                <div class="mb-6">
                    <label class="block text-gray-400 mb-2">Your Referral Link:</label>
                    <div class="flex">
                        <input type="text" id="referralLink" value="https://cryptoinvest.pro/ref/CRY001234" 
                               class="flex-1 bg-crypto-card border border-gray-600 rounded-l-lg px-4 py-3 text-white" readonly>
                        <button onclick="copyReferralLink()" 
                                class="bg-crypto-gold text-black px-6 py-3 rounded-r-lg font-semibold hover:bg-yellow-400 transition-all">
                            Copy
                        </button>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button onclick="shareToSocial('whatsapp')" 
                            class="bg-green-600 hover:bg-green-700 p-4 rounded-lg text-center transition-all hover-glow">
                        <i class="fab fa-whatsapp text-2xl mb-2"></i>
                        <p class="text-sm">WhatsApp</p>
                    </button>
                    <button onclick="shareToSocial('telegram')" 
                            class="bg-blue-500 hover:bg-blue-600 p-4 rounded-lg text-center transition-all hover-glow">
                        <i class="fab fa-telegram text-2xl mb-2"></i>
                        <p class="text-sm">Telegram</p>
                    </button>
                    <button onclick="shareToSocial('facebook')" 
                            class="bg-blue-700 hover:bg-blue-800 p-4 rounded-lg text-center transition-all hover-glow">
                        <i class="fab fa-facebook text-2xl mb-2"></i>
                        <p class="text-sm">Facebook</p>
                    </button>
                    <button onclick="shareToSocial('instagram')" 
                            class="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 p-4 rounded-lg text-center transition-all hover-glow">
                        <i class="fab fa-instagram text-2xl mb-2"></i>
                        <p class="text-sm">Instagram</p>
                    </button>
                </div>
                
                <div class="mt-6 p-4 bg-crypto-card rounded-lg">
                    <h3 class="font-semibold mb-2">Referral Benefits:</h3>
                    <ul class="text-gray-300 space-y-1">
                        <li>• Earn 10% commission on referral deposits</li>
                        <li>• Get $5 bonus for each successful referral</li>
                        <li>• Unlock premium features with 5+ referrals</li>
                    </ul>
                </div>
            </div>
        </div>
    `;
  return section;
}

// Create Deposit Section
function createDepositSection() {
  const section = document.createElement("div");
  section.id = "deposit-section";
  section.className = "hidden";
  section.innerHTML = `
        <div class="container mx-auto space-y-6">
            <div class="glassmorphism p-6 rounded-lg">
                <h2 class="text-2xl font-bold mb-6 gradient-text">💰 Deposit Funds</h2>
                
                <form id="depositForm" class="space-y-4">
                    <div>
                        <label class="block text-gray-400 mb-2">Deposit Amount ($)</label>
                        <input type="number" id="depositAmount" min="10" step="0.01" 
                               class="w-full bg-crypto-card border border-gray-600 rounded-lg px-4 py-3 text-white" 
                               placeholder="Enter amount (min $10)" required>
                    </div>
                    
                    <div>
                        <label class="block text-gray-400 mb-2">Payment Method</label>
                        <select id="paymentMethod" 
                                class="w-full bg-crypto-card border border-gray-600 rounded-lg px-4 py-3 text-white" required>
                            <option value="">Select payment method</option>
                            <option value="bank">Bank Transfer</option>
                            <option value="card">Credit/Debit Card</option>
                            <option value="crypto">Cryptocurrency</option>
                            <option value="paypal">PayPal</option>
                        </select>
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-gradient-gold text-black py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-all hover-glow">
                        Proceed to Deposit
                    </button>
                </form>
            </div>
            
            <div class="glassmorphism p-6 rounded-lg">
                <h3 class="text-xl font-semibold mb-4 gradient-text">📊 Deposit History</h3>
                <div id="depositHistory" class="space-y-3"></div>
            </div>
        </div>
    `;
  return section;
}

// Create Top-up Section
function createTopupSection() {
  const section = document.createElement("div");
  section.id = "topup-section";
  section.className = "hidden";
  section.innerHTML = `
        <div class="container mx-auto space-y-6">
            <div class="glassmorphism p-6 rounded-lg">
                <h2 class="text-2xl font-bold mb-6 gradient-text">🔋 Top-up Account</h2>

                <form id="topupForm" class="space-y-4">
                    <div>
                        <label class="block text-gray-400 mb-2">Top-up Amount ($)</label>
                        <input type="number" id="topupAmount" min="5" step="0.01"
                               class="w-full bg-crypto-card border border-gray-600 rounded-lg px-4 py-3 text-white"
                               placeholder="Enter amount (min $5)" required>
                    </div>

                    <div>
                        <label class="block text-gray-400 mb-2">Top-up Type</label>
                        <select id="topupType"
                                class="w-full bg-crypto-card border border-gray-600 rounded-lg px-4 py-3 text-white" required>
                            <option value="">Select top-up type</option>
                            <option value="balance">Account Balance</option>
                            <option value="trading">Trading Balance</option>
                            <option value="staking">Staking Balance</option>
                        </select>
                    </div>

                    <button type="submit"
                            class="w-full bg-gradient-purple text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-all hover-glow">
                        Top-up Account
                    </button>
                </form>
            </div>

            <div class="glassmorphism p-6 rounded-lg">
                <h3 class="text-xl font-semibold mb-4 gradient-text">📊 Top-up History</h3>
                <div id="topupHistory" class="space-y-3"></div>
            </div>
        </div>
    `;
  return section;
}

// Create Transfer Section
function createTransferSection() {
  const section = document.createElement("div");
  section.id = "transfer-section";
  section.className = "hidden";
  section.innerHTML = `
        <div class="container mx-auto space-y-6">
            <div class="glassmorphism p-6 rounded-lg">
                <h2 class="text-2xl font-bold mb-6 gradient-text">💸 Transfer Crypto</h2>

                <form id="transferForm" class="space-y-4">
                    <div>
                        <label class="block text-gray-400 mb-2">Transfer Amount ($)</label>
                        <input type="number" id="transferAmount" min="1" step="0.01"
                               class="w-full bg-crypto-card border border-gray-600 rounded-lg px-4 py-3 text-white"
                               placeholder="Enter amount" required>
                    </div>

                    <div>
                        <label class="block text-gray-400 mb-2">Recipient Wallet Address</label>
                        <input type="text" id="recipientAddress"
                               class="w-full bg-crypto-card border border-gray-600 rounded-lg px-4 py-3 text-white"
                               placeholder="Enter wallet address" required>
                    </div>

                    <div>
                        <label class="block text-gray-400 mb-2">Cryptocurrency</label>
                        <select id="transferCrypto"
                                class="w-full bg-crypto-card border border-gray-600 rounded-lg px-4 py-3 text-white" required>
                            <option value="">Select cryptocurrency</option>
                            <option value="BTC">Bitcoin (BTC)</option>
                            <option value="ETH">Ethereum (ETH)</option>
                            <option value="USDT">Tether (USDT)</option>
                            <option value="BNB">Binance Coin (BNB)</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-gray-400 mb-2">Transfer Note (Optional)</label>
                        <textarea id="transferNote" rows="3"
                                  class="w-full bg-crypto-card border border-gray-600 rounded-lg px-4 py-3 text-white"
                                  placeholder="Add a note for this transfer"></textarea>
                    </div>

                    <button type="submit"
                            class="w-full bg-gradient-to-r from-red-500 to-pink-500 text-white py-3 rounded-lg font-semibold hover:from-red-600 hover:to-pink-600 transition-all hover-glow">
                        Transfer Crypto
                    </button>
                </form>
            </div>

            <div class="glassmorphism p-6 rounded-lg">
                <h3 class="text-xl font-semibold mb-4 gradient-text">📊 Transfer History</h3>
                <div id="transferHistory" class="space-y-3"></div>
            </div>
        </div>
    `;
  return section;
}

// Create Stacking Section
function createStackingSection() {
  const section = document.createElement("div");
  section.id = "stacking-section";
  section.className = "hidden";
  section.innerHTML = `
        <div class="container mx-auto space-y-6">
            <div class="glassmorphism p-6 rounded-lg">
                <h2 class="text-2xl font-bold mb-6 gradient-text">🪙 Stacking BTC</h2>

                <div class="bg-yellow-900 border border-yellow-600 p-4 rounded-lg mb-6">
                    <p class="text-yellow-200 font-semibold">⚠️ Important Information:</p>
                    <p class="text-yellow-100 mt-2">Stacking Coin time minimum 6 months. You can withdraw after 6 months period.</p>
                </div>

                <form id="stackingForm" class="space-y-4">
                    <div>
                        <label class="block text-gray-400 mb-2">Stacking Amount (BTC)</label>
                        <input type="number" id="stackingAmount" min="0.001" step="0.001"
                               class="w-full bg-crypto-card border border-gray-600 rounded-lg px-4 py-3 text-white"
                               placeholder="Enter BTC amount (min 0.001)" required>
                    </div>

                    <div>
                        <label class="block text-gray-400 mb-2">Stacking Duration</label>
                        <select id="stackingDuration"
                                class="w-full bg-crypto-card border border-gray-600 rounded-lg px-4 py-3 text-white" required>
                            <option value="">Select duration</option>
                            <option value="6">6 Months (8% APY)</option>
                            <option value="12">12 Months (12% APY)</option>
                            <option value="24">24 Months (18% APY)</option>
                        </select>
                    </div>

                    <div class="bg-crypto-card p-4 rounded-lg">
                        <h4 class="font-semibold mb-2 text-crypto-gold">Estimated Returns:</h4>
                        <div id="stackingEstimate" class="text-gray-300">
                            <p>Select amount and duration to see estimated returns</p>
                        </div>
                    </div>

                    <button type="submit"
                            class="w-full bg-gradient-to-r from-green-500 to-blue-500 text-white py-3 rounded-lg font-semibold hover:from-green-600 hover:to-blue-600 transition-all hover-glow">
                        Start Stacking
                    </button>
                </form>
            </div>

            <div class="glassmorphism p-6 rounded-lg">
                <h3 class="text-xl font-semibold mb-4 gradient-text">📊 Stacking History</h3>
                <div id="stackingHistory" class="space-y-3"></div>
            </div>
        </div>
    `;
  return section;
}

// Show specific section
function showSection(sectionName) {
  // Hide all sections
  const sections = [
    "dashboard",
    "refer",
    "deposit",
    "topup",
    "transfer",
    "stacking",
  ];
  sections.forEach((section) => {
    const element = document.getElementById(`${section}-section`);
    if (element) element.classList.add("hidden");
  });

  // Show dashboard cards or specific section
  if (sectionName === "dashboard") {
    document.querySelector("main .container .grid").classList.remove("hidden");
  } else {
    document.querySelector("main .container .grid").classList.add("hidden");
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
      targetSection.classList.remove("hidden");
      loadSectionData(sectionName);
    }
  }

  currentSection = sectionName;
  updateActiveNavButton(sectionName);
}

// Update active navigation button
function updateActiveNavButton(activeSection) {
  document.querySelectorAll(".nav-btn").forEach((btn) => {
    btn.classList.remove("bg-crypto-gold", "text-black");
  });

  const activeBtn = document.querySelector(
    `[onclick="showSection('${activeSection}')"]`
  );
  if (activeBtn) {
    activeBtn.classList.add("bg-crypto-gold", "text-black");
  }
}

// Load section-specific data
function loadSectionData(section) {
  switch (section) {
    case "deposit":
      loadTransactionHistory("deposit", transactions.deposits);
      break;
    case "topup":
      loadTransactionHistory("topup", transactions.topups);
      break;
    case "transfer":
      loadTransactionHistory("transfer", transactions.transfers);
      break;
    case "stacking":
      loadTransactionHistory("stacking", transactions.stacking);
      break;
  }
}

// Load transaction history
function loadTransactionHistory(type, data) {
  const container = document.getElementById(`${type}History`);
  if (!container) return;

  if (data.length === 0) {
    container.innerHTML =
      '<p class="text-gray-400 text-center py-4">No transactions yet</p>';
    return;
  }

  container.innerHTML = data
    .map(
      (transaction) => `
        <div class="bg-crypto-card p-4 rounded-lg flex justify-between items-center">
            <div>
                <p class="font-semibold">$${transaction.amount}</p>
                <p class="text-gray-400 text-sm">${transaction.date}</p>
                ${
                  transaction.method
                    ? `<p class="text-gray-400 text-sm">${transaction.method}</p>`
                    : ""
                }
                ${
                  transaction.coin
                    ? `<p class="text-gray-400 text-sm">${transaction.coin}</p>`
                    : ""
                }
            </div>
            <span class="px-3 py-1 rounded-full text-sm ${
              transaction.status === "Completed"
                ? "bg-green-600 text-green-100"
                : transaction.status === "Active"
                ? "bg-blue-600 text-blue-100"
                : "bg-yellow-600 text-yellow-100"
            }">
                ${transaction.status}
            </span>
        </div>
    `
    )
    .join("");
}

// Utility functions
function copyReferralLink() {
  const linkInput = document.getElementById("referralLink");
  linkInput.select();
  document.execCommand("copy");

  // Show feedback
  const button = event.target;
  const originalText = button.textContent;
  button.textContent = "Copied!";
  setTimeout(() => {
    button.textContent = originalText;
  }, 2000);
}

function shareToSocial(platform) {
  const referralLink = document.getElementById("referralLink").value;
  const message = "Join me on CryptoInvest Pro and start earning! ";

  let shareUrl = "";
  switch (platform) {
    case "whatsapp":
      shareUrl = `https://wa.me/?text=${encodeURIComponent(
        message + referralLink
      )}`;
      break;
    case "telegram":
      shareUrl = `https://t.me/share/url?url=${encodeURIComponent(
        referralLink
      )}&text=${encodeURIComponent(message)}`;
      break;
    case "facebook":
      shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
        referralLink
      )}`;
      break;
    case "instagram":
      // Instagram doesn't support direct URL sharing, copy to clipboard instead
      navigator.clipboard.writeText(message + referralLink);
      alert("Link copied to clipboard! You can paste it in Instagram.");
      return;
  }

  if (shareUrl) {
    window.open(shareUrl, "_blank");
  }
}

// Form handlers
document.addEventListener("submit", function (e) {
  if (e.target.id === "depositForm") {
    e.preventDefault();
    handleDeposit();
  } else if (e.target.id === "topupForm") {
    e.preventDefault();
    handleTopup();
  } else if (e.target.id === "transferForm") {
    e.preventDefault();
    handleTransfer();
  } else if (e.target.id === "stackingForm") {
    e.preventDefault();
    handleStacking();
  }
});

function handleDeposit() {
  const amount = document.getElementById("depositAmount").value;
  const method = document.getElementById("paymentMethod").value;

  if (amount && method) {
    // Simulate deposit processing
    alert(`Deposit of $${amount} via ${method} is being processed!`);

    // Add to transaction history
    transactions.deposits.unshift({
      id: Date.now(),
      amount: parseFloat(amount),
      date: new Date().toISOString().split("T")[0],
      status: "Processing",
      method: method,
    });

    // Reset form and reload history
    document.getElementById("depositForm").reset();
    loadTransactionHistory("deposit", transactions.deposits);
  }
}

function handleTopup() {
  const amount = document.getElementById("topupAmount").value;
  const type = document.getElementById("topupType").value;

  if (amount && type) {
    // Simulate topup processing
    alert(`Top-up of $${amount} to ${type} completed!`);

    // Add to transaction history
    transactions.topups.unshift({
      id: Date.now(),
      amount: parseFloat(amount),
      date: new Date().toISOString().split("T")[0],
      status: "Completed",
      type: type,
    });

    // Reset form and reload history
    document.getElementById("topupForm").reset();
    loadTransactionHistory("topup", transactions.topups);
  }
}

function handleTransfer() {
  const amount = document.getElementById("transferAmount").value;
  const address = document.getElementById("recipientAddress").value;
  const crypto = document.getElementById("transferCrypto").value;
  const note = document.getElementById("transferNote").value;

  if (amount && address && crypto) {
    // Simulate transfer processing
    alert(
      `Transfer of $${amount} ${crypto} to ${address.substring(
        0,
        10
      )}... is being processed!`
    );

    // Add to transaction history
    transactions.transfers.unshift({
      id: Date.now(),
      amount: parseFloat(amount),
      date: new Date().toISOString().split("T")[0],
      status: "Processing",
      to: `${crypto} Wallet`,
      note: note,
    });

    // Reset form and reload history
    document.getElementById("transferForm").reset();
    loadTransactionHistory("transfer", transactions.transfers);
  }
}

function handleStacking() {
  const amount = document.getElementById("stackingAmount").value;
  const duration = document.getElementById("stackingDuration").value;

  if (amount && duration) {
    // Simulate stacking processing
    alert(`Stacking of ${amount} BTC for ${duration} months has started!`);

    // Add to transaction history
    transactions.stacking.unshift({
      id: Date.now(),
      amount: parseFloat(amount),
      coin: "BTC",
      date: new Date().toISOString().split("T")[0],
      status: "Active",
      duration: `${duration} months`,
    });

    // Reset form and reload history
    document.getElementById("stackingForm").reset();
    loadTransactionHistory("stacking", transactions.stacking);
  }
}

// Stacking calculator
function updateStackingEstimate() {
  const amount = document.getElementById("stackingAmount")?.value;
  const duration = document.getElementById("stackingDuration")?.value;
  const estimateDiv = document.getElementById("stackingEstimate");

  if (!amount || !duration || !estimateDiv) return;

  const btcPrice = 45000; // Assuming $45,000 per BTC
  const usdAmount = parseFloat(amount) * btcPrice;

  let apy = 0;
  switch (duration) {
    case "6":
      apy = 0.08;
      break;
    case "12":
      apy = 0.12;
      break;
    case "24":
      apy = 0.18;
      break;
  }

  const monthlyReturn = (usdAmount * apy) / 12;
  const totalReturn = monthlyReturn * parseInt(duration);
  const finalAmount = usdAmount + totalReturn;

  estimateDiv.innerHTML = `
        <div class="space-y-2">
            <p>Investment: ${amount} BTC (~$${usdAmount.toLocaleString()})</p>
            <p>Monthly Return: ~$${monthlyReturn.toFixed(2)}</p>
            <p>Total Return: ~$${totalReturn.toFixed(2)}</p>
            <p class="font-bold text-crypto-gold">Final Amount: ~$${finalAmount.toFixed(
              2
            )}</p>
        </div>
    `;
}

// Add event listeners for stacking calculator
document.addEventListener("DOMContentLoaded", function () {
  setTimeout(() => {
    const stackingAmount = document.getElementById("stackingAmount");
    const stackingDuration = document.getElementById("stackingDuration");

    if (stackingAmount && stackingDuration) {
      stackingAmount.addEventListener("input", updateStackingEstimate);
      stackingDuration.addEventListener("change", updateStackingEstimate);
    }
  }, 100);
});
