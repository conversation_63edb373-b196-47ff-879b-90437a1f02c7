<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Investment Dashboard - Profile</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #111827; /* bg-gray-900 */
            color: white;
            min-height: 100vh;
            padding-bottom: 70px; /* Space for bottom nav */
            position: relative;
        }
        .glassmorphism {
            background: rgba(31, 41, 55, 0.5); /* bg-gray-800 with opacity */
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #111827;
        }
        ::-webkit-scrollbar-thumb {
            background: #374151;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #4b5563;
        }
        /* Important: Make sure bottom navigation is always visible */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background-color: rgba(17, 24, 39, 0.8);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(75, 85, 99, 0.5);
            padding: 0.5rem;
            display: flex;
            justify-content: space-around;
        }
        @media (min-width: 768px) {
            .bottom-nav {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-900 text-white">

    <!-- Top Header -->
    <div class="bg-gray-900/80 backdrop-blur-lg sticky top-0 z-40">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-amber-500 text-transparent bg-clip-text">
                    CryptoUI
                </div>
                <div class="flex items-center">
                    <nav class="hidden md:flex items-center space-x-8">
                        <a href="dashboard.html" class="text-gray-300 hover:text-yellow-400 transition-colors">Dashboard</a>
                        <a href="wallet.html" class="text-gray-300 hover:text-yellow-400 transition-colors">Wallet</a>
                        <a href="team.html" class="text-gray-300 hover:text-yellow-400 transition-colors">Team</a>
                        <a href="profile.html" class="text-yellow-400 transition-colors">Profile</a>
                    </nav>
                    <div class="flex items-center space-x-4 md:ml-8">
                        <button class="relative text-gray-300 hover:text-yellow-400 p-2 rounded-full hover:bg-gray-800">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path></svg>
                            <span class="absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-gray-900"></span>
                        </button>
                        <button class="flex text-sm border-2 border-transparent rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                            <img class="h-8 w-8 rounded-full" src="https://i.pravatar.cc/40?u=alexdoe" alt="User avatar">
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto p-4 pb-24">
        <main class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="md:col-span-3 space-y-8">
                <section id="user-info" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800">
                    <h3 class="text-xl font-semibold mb-4 text-gray-200">User Information</h3>
                    <div class="space-y-3 text-gray-300">
                        <p>User Name: <span class="font-semibold text-white float-right">Alex Doe</span></p>
                        <p>User ID: <span class="font-semibold text-white float-right">AD-12345</span></p>
                        <p>Register Date: <span class="font-semibold text-white float-right">2023-01-15</span></p>
                        <p>Topup Date: <span class="font-semibold text-white float-right">2023-06-20</span></p>
                        <p>Account Type: <span class="font-semibold text-yellow-400 float-right">Trial ($10)</span></p>
                        <p>Crypto Wallet Balance: <span class="font-semibold text-white float-right">$1,250.75</span></p>
                    </div>
                </section>

                <section id="profit-info" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800">
                    <h3 class="text-xl font-semibold mb-4 text-gray-200">Profit Information</h3>
                    <div class="space-y-3 text-gray-300">
                        <p>Today’s Profit: <span class="font-semibold text-green-400 float-right">$55.20</span></p>
                        <p>Total Profit: <span class="font-semibold text-green-400 float-right">$480.50</span></p>
                    </div>
                </section>

                <section id="refer-link" class="p-6 rounded-2xl shadow-lg glassmorphism bg-gray-800">
                    <h3 class="text-xl font-semibold mb-4 text-gray-200">Refer & Earn</h3>
                    <div class="bg-gray-900 p-3 rounded-lg text-center mb-4">
                        <p class="text-yellow-400 break-words">https://yourcrypto.link/ref/12345</p>
                    </div>
                    <div class="flex justify-center space-x-3">
                        <a href="#" class="p-3 bg-green-500 rounded-full hover:bg-green-600 transition-transform transform hover:scale-110"><svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M12.04 2C6.58 2 2.13 6.45 2.13 11.91C2.13 13.66 2.59 15.35 3.45 16.86L2.06 22L7.31 20.58C8.76 21.39 10.37 21.82 12.04 21.82C17.5 21.82 21.95 17.37 21.95 11.91C21.95 6.45 17.5 2 12.04 2M12.04 20.13C10.56 20.13 9.12 19.74 7.89 19L7.5 18.78L4.42 19.65L5.32 16.69L5.07 16.29C4.24 14.98 3.82 13.47 3.82 11.91C3.82 7.39 7.52 3.7 12.04 3.7C16.56 3.7 20.26 7.39 20.26 11.91C20.26 16.43 16.56 20.13 12.04 20.13M17.41 14.27C17.13 14.13 15.93 13.54 15.68 13.45C15.44 13.36 15.26 13.31 15.08 13.6C14.9 13.88 14.39 14.49 14.25 14.63C14.11 14.77 13.97 14.8 13.73 14.71C13.49 14.61 12.55 14.29 11.41 13.26C10.53 12.48 9.92 11.55 9.78 11.27C9.64 10.99 9.76 10.85 9.88 10.73C10 10.61 10.16 10.4 10.32 10.22C10.48 10.04 10.53 9.92 10.62 9.73C10.71 9.54 10.67 9.38 10.59 9.24C10.52 9.1 9.92 7.64 9.68 7.07C9.44 6.5 9.2 6.55 9.04 6.54H8.5C8.34 6.54 8.06 6.63 7.82 6.88C7.58 7.12 7 7.69 7 8.86C7 10.03 7.86 11.15 8 11.3C8.14 11.44 9.92 14.16 12.62 15.43C15.32 16.7 15.32 16.23 15.82 16.19C16.32 16.14 17.22 15.54 17.41 15.26C17.6 14.98 17.6 14.69 17.54 14.59C17.48 14.49 17.41 14.27 17.41 14.27Z"></path></svg></a>
                        <a href="#" class="p-3 bg-blue-500 rounded-full hover:bg-blue-600 transition-transform transform hover:scale-110"><svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M9.78 18.65l.28-4.23-7.02-2.14c-.99-.3-1.42-1.46-.72-2.27l16.16-18.98c.78-.91 2.04-.22 1.53.93L15.4 21.1c-.39.9-1.5.99-2.05.24l-3.57-3.69z"></path></svg></a>
                        <a href="#" class="p-3 bg-blue-800 rounded-full hover:bg-blue-900 transition-transform transform hover:scale-110"><svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878V14.89h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"></path></svg></a>
                        <a href="#" class="p-3 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-full hover:opacity-90 transition-transform transform hover:scale-110"><svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fill-rule="evenodd" d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 16c-3.314 0-6-2.686-6-6s2.686-6 6-6 6 2.686 6 6-2.686 6-6 6zm0-10c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm6-2a1 1 0 110-2 1 1 0 010 2z" clip-rule="evenodd"></path></svg></a>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Bottom Navigation (Mobile Only) -->
    <nav class="bottom-nav">
        <a href="dashboard.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6">
                </path>
            </svg>
            <span class="text-xs mt-1">Home</span>
        </a>
        <a href="wallet.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H4a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
            <span class="text-xs mt-1">Wallet</span>
        </a>
        <a href="team.html" class="flex flex-col items-center text-gray-300 hover:text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.653-.25-1.26-.698-1.702M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.653.25-1.26.698-1.702m12.302 0A5.002 5.002 0 0017 13h-2a5.002 5.002 0 00-4.302 2.298m-6.696 0A5.002 5.002 0 017 13H5a5.002 5.002 0 014.302-2.298M12 13a3 3 0 100-6 3 3 0 000 6z">
                </path>
            </svg>
            <span class="text-xs mt-1">Team</span>
        </a>
        <a href="profile.html" class="flex flex-col items-center text-yellow-400 p-2">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="text-xs mt-1">Profile</span>
        </a>
    </nav>

</body>
</html>