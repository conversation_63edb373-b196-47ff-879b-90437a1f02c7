<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Dashboard - Theme Showcase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        /* Theme 1: Dark Premium (Current) */
        .theme-dark-premium {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: white;
        }

        .theme-dark-premium .card {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .theme-dark-premium .accent {
            color: #ffd700;
        }

        .theme-dark-premium .secondary {
            color: #6b46c1;
        }

        /* Theme 2: Deep Ocean */
        .theme-ocean {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
        }

        .theme-ocean .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .theme-ocean .accent {
            color: #3b82f6;
        }

        .theme-ocean .secondary {
            color: #06b6d4;
        }

        /* Theme 3: Midnight Purple */
        .theme-purple {
            background: linear-gradient(135deg, #0f0a1a 0%, #1a1625 100%);
            color: white;
        }

        .theme-purple .card {
            background: rgba(26, 22, 37, 0.85);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(139, 92, 246, 0.15);
        }

        .theme-purple .accent {
            color: #8b5cf6;
        }

        .theme-purple .secondary {
            color: #a78bfa;
        }

        /* Theme 4: Forest Green */
        .theme-forest {
            background: linear-gradient(135deg, #0f2027 0%, #203a43 100%);
            color: white;
        }

        .theme-forest .card {
            background: rgba(32, 58, 67, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .theme-forest .accent {
            color: #22c55e;
        }

        .theme-forest .secondary {
            color: #10b981;
        }

        /* Theme 5: Sunset Orange */
        .theme-sunset {
            background: linear-gradient(135deg, #1a0f0a 0%, #2d1810 100%);
            color: white;
        }

        .theme-sunset .card {
            background: rgba(45, 24, 16, 0.85);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(249, 115, 22, 0.15);
        }

        .theme-sunset .accent {
            color: #f97316;
        }

        .theme-sunset .secondary {
            color: #fb923c;
        }

        /* Theme 6: Arctic Blue */
        .theme-arctic {
            background: linear-gradient(135deg, #0c1426 0%, #1e3a5f 100%);
            color: white;
        }

        .theme-arctic .card {
            background: rgba(30, 58, 95, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(56, 189, 248, 0.2);
        }

        .theme-arctic .accent {
            color: #38bdf8;
        }

        .theme-arctic .secondary {
            color: #0ea5e9;
        }

        .glassmorphism {
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .coin-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }
        }

        .theme-selector {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        /* Professional Navigation Enhancements */
        .nav-btn {
            position: relative;
            overflow: hidden;
        }

        .nav-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-btn:hover::before {
            left: 100%;
        }

        /* Theme-specific navigation shadows */
        .theme-dark-premium .nav-btn:hover {
            box-shadow: 0 4px 20px rgba(255, 215, 0, 0.15);
        }

        .theme-ocean .nav-btn:hover {
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
        }

        .theme-purple .nav-btn:hover {
            box-shadow: 0 4px 20px rgba(139, 92, 246, 0.15);
        }

        .theme-forest .nav-btn:hover {
            box-shadow: 0 4px 20px rgba(34, 197, 94, 0.15);
        }

        .theme-sunset .nav-btn:hover {
            box-shadow: 0 4px 20px rgba(249, 115, 22, 0.15);
        }

        .theme-arctic .nav-btn:hover {
            box-shadow: 0 4px 20px rgba(56, 189, 248, 0.15);
        }

        /* Professional Header Enhancements */
        header {
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        /* Navigation link animations */
        nav a {
            position: relative;
            overflow: hidden;
        }

        nav a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            transition: left 0.6s ease;
        }

        nav a:hover::before {
            left: 100%;
        }

        /* Sleek Logo Glow Animation - Theme Adaptive */
        .logo-glow {
            position: relative;
            transition: all 0.3s ease;
        }

        .logo-glow::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 14px;
            opacity: 0.1;
            animation: logoRotate 4s linear infinite;
            z-index: -1;
        }

        .logo-glow:hover::before {
            opacity: 0.4;
        }

        @keyframes logoRotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Theme-specific logo glows */
        .theme-dark-premium .logo-glow::before {
            background: linear-gradient(45deg, transparent, #ffd700, transparent);
        }

        .theme-ocean .logo-glow::before {
            background: linear-gradient(45deg, transparent, #3b82f6, transparent);
        }

        .theme-purple .logo-glow::before {
            background: linear-gradient(45deg, transparent, #8b5cf6, transparent);
        }

        .theme-forest .logo-glow::before {
            background: linear-gradient(45deg, transparent, #22c55e, transparent);
        }

        .theme-sunset .logo-glow::before {
            background: linear-gradient(45deg, transparent, #f97316, transparent);
        }

        .theme-arctic .logo-glow::before {
            background: linear-gradient(45deg, transparent, #38bdf8, transparent);
        }

        /* Sleek Notification Pulse */
        .notification-badge {
            position: relative;
            overflow: hidden;
        }

        .notification-badge::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(239, 68, 68, 0.4);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: notificationRipple 2s ease-out infinite;
        }

        @keyframes notificationRipple {
            0% {
                width: 0;
                height: 0;
                opacity: 1;
            }

            100% {
                width: 20px;
                height: 20px;
                opacity: 0;
            }
        }

        /* Sleek Navigation Underline */
        .nav-link {
            position: relative;
            overflow: hidden;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, currentColor, transparent);
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Sleek Button Hover Effects */
        .header-btn {
            position: relative;
            overflow: hidden;
        }

        .header-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }

        .header-btn:hover::before {
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }
    </style>
</head>

<body id="themeBody" class="theme-dark-premium min-h-screen transition-all duration-500">

    <!-- Theme Selector -->
    <div class="theme-selector">
        <select id="themeSelect" class="bg-gray-800 text-white p-2 rounded-lg border border-gray-600">
            <option value="theme-dark-premium">Dark Premium (Gold)</option>
            <option value="theme-ocean">Deep Ocean (Blue)</option>
            <option value="theme-purple">Midnight Purple</option>
            <option value="theme-forest">Forest Green</option>
            <option value="theme-sunset">Sunset Orange</option>
            <option value="theme-arctic">Arctic Blue</option>
        </select>
    </div>

    <!-- Header -->
    <header class="sticky top-0 z-50 backdrop-blur-xl bg-black bg-opacity-40 border-b border-white border-opacity-10">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <!-- Logo & Brand -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 accent rounded-xl flex items-center justify-center shadow-lg logo-glow">
                        <i class="fas fa-chart-line text-lg text-black font-bold"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold accent">CryptoInvest</h1>
                        <p class="text-xs text-gray-400 font-medium">Professional Trading</p>
                    </div>
                </div>

                <!-- Center Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="#"
                        class="nav-link text-sm font-medium text-gray-300 hover:text-white accent transition-colors duration-200">
                        Dashboard
                    </a>
                    <a href="#"
                        class="nav-link text-sm font-medium text-gray-300 hover:text-white accent transition-colors duration-200">
                        Portfolio
                    </a>
                    <a href="#"
                        class="nav-link text-sm font-medium text-gray-300 hover:text-white accent transition-colors duration-200">
                        Markets
                    </a>
                    <a href="#"
                        class="nav-link text-sm font-medium text-gray-300 hover:text-white accent transition-colors duration-200">
                        Analytics
                    </a>
                </nav>

                <!-- Right Actions -->
                <div class="flex items-center space-x-4">
                    <!-- Search -->
                    <button
                        class="header-btn hidden md:flex w-9 h-9 rounded-lg items-center justify-center transition-all duration-200 group">
                        <i class="fas fa-search text-sm text-gray-400 group-hover:text-white"></i>
                    </button>

                    <!-- Notifications -->
                    <button
                        class="header-btn notification-badge relative w-9 h-9 rounded-lg flex items-center justify-center transition-all duration-200 group">
                        <i
                            class="fas fa-bell text-sm accent group-hover:scale-110 transition-transform duration-200"></i>
                        <span
                            class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="w-1.5 h-1.5 bg-white rounded-full"></span>
                        </span>
                    </button>

                    <!-- Settings -->
                    <button
                        class="header-btn hidden md:flex w-9 h-9 rounded-lg items-center justify-center transition-all duration-200 group">
                        <i
                            class="fas fa-cog text-sm text-gray-400 group-hover:text-white group-hover:rotate-90 transition-all duration-300"></i>
                    </button>

                    <!-- User Profile -->
                    <div class="flex items-center space-x-3 pl-2">
                        <div class="hidden md:block text-right">
                            <p class="text-sm font-semibold text-white">John Doe</p>
                            <p class="text-xs text-gray-400">Premium Member</p>
                        </div>
                        <button
                            class="w-10 h-10 accent rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 group">
                            <i
                                class="fas fa-user text-sm text-black group-hover:scale-110 transition-transform duration-200"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Alert Banner -->
    <div class="m-4">
        <div class="card glassmorphism p-4 rounded-lg text-center">
            <p class="accent font-bold text-lg">🚨 LIMITED TIME OFFER: Get 20% Bonus on First Deposit! 🚨</p>
        </div>
    </div>

    <!-- Main Dashboard -->
    <main class="p-4 pb-20 space-y-6">

        <!-- Stats Cards Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

            <!-- Portfolio Value -->
            <div class="card glassmorphism p-6 rounded-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 accent rounded-full flex items-center justify-center">
                            <i class="fas fa-wallet text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold">Portfolio</h3>
                            <p class="text-gray-400 text-sm">Total Value</p>
                        </div>
                    </div>
                </div>
                <p class="text-2xl font-bold accent">$126,490</p>
                <p class="text-gray-400 text-sm mt-1">Current Balance</p>
            </div>

            <!-- Today's P&L -->
            <div class="card glassmorphism p-6 rounded-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 secondary rounded-full flex items-center justify-center">
                            <i class="fas fa-chart-line text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold">Today's P&L</h3>
                            <p class="text-gray-400 text-sm">Profit/Loss</p>
                        </div>
                    </div>
                </div>
                <p class="text-2xl font-bold secondary">$2,845</p>
                <p class="text-gray-400 text-sm mt-1">Daily Change</p>
            </div>

            <!-- Active Positions -->
            <div class="card glassmorphism p-6 rounded-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 accent rounded-full flex items-center justify-center">
                            <i class="fas fa-coins text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold">Positions</h3>
                            <p class="text-gray-400 text-sm">Active</p>
                        </div>
                    </div>
                </div>
                <p class="text-2xl font-bold">12</p>
                <p class="text-gray-400 text-sm mt-1">Holdings</p>
            </div>

            <!-- Staking Rewards -->
            <div class="card glassmorphism p-6 rounded-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 secondary rounded-full flex items-center justify-center">
                            <i class="fas fa-percentage text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold">Staking</h3>
                            <p class="text-gray-400 text-sm">Rewards</p>
                        </div>
                    </div>
                </div>
                <p class="text-2xl font-bold secondary">$1,234</p>
                <p class="text-gray-400 text-sm mt-1">Monthly Yield</p>
            </div>
        </div>

        <!-- Crypto Holdings -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

            <!-- Bitcoin Card -->
            <div class="card glassmorphism p-6 rounded-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="coin-pulse">
                            <img src="https://cryptologos.cc/logos/bitcoin-btc-logo.png" alt="BTC" class="w-12 h-12">
                        </div>
                        <div>
                            <h3 class="text-lg font-bold">Bitcoin</h3>
                            <p class="text-gray-400 text-sm">BTC</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-xl font-bold accent">$63,354</p>
                        <p class="text-gray-400 text-sm">BTC/USD</p>
                    </div>
                </div>
                <div class="bg-black bg-opacity-20 p-3 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400 text-sm">Your Stack:</span>
                        <span class="text-white font-semibold">10 BTC</span>
                    </div>
                    <div class="flex justify-between items-center mt-1">
                        <span class="text-gray-400 text-sm">Value:</span>
                        <span class="accent font-bold">$633,540</span>
                    </div>
                </div>
            </div>

            <!-- Ethereum Card -->
            <div class="card glassmorphism p-6 rounded-lg">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="coin-pulse">
                            <img src="https://cryptologos.cc/logos/ethereum-eth-logo.png" alt="ETH" class="w-12 h-12">
                        </div>
                        <div>
                            <h3 class="text-lg font-bold">Ethereum</h3>
                            <p class="text-gray-400 text-sm">ETH</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-xl font-bold secondary">$3,245</p>
                        <p class="text-gray-400 text-sm">ETH/USD</p>
                    </div>
                </div>
                <div class="bg-black bg-opacity-20 p-3 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400 text-sm">Your Stack:</span>
                        <span class="text-white font-semibold">5.5 ETH</span>
                    </div>
                    <div class="flex justify-between items-center mt-1">
                        <span class="text-gray-400 text-sm">Value:</span>
                        <span class="secondary font-bold">$17,847</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <button class="card glassmorphism p-4 rounded-lg text-center hover:scale-105 transition-transform">
                <i class="fas fa-share-alt text-2xl accent mb-2"></i>
                <p class="text-sm font-semibold">Refer</p>
            </button>
            <button class="card glassmorphism p-4 rounded-lg text-center hover:scale-105 transition-transform">
                <i class="fas fa-plus-circle text-2xl secondary mb-2"></i>
                <p class="text-sm font-semibold">Deposit</p>
            </button>
            <button class="card glassmorphism p-4 rounded-lg text-center hover:scale-105 transition-transform">
                <i class="fas fa-wallet text-2xl accent mb-2"></i>
                <p class="text-sm font-semibold">Top-up</p>
            </button>
            <button class="card glassmorphism p-4 rounded-lg text-center hover:scale-105 transition-transform">
                <i class="fas fa-exchange-alt text-2xl secondary mb-2"></i>
                <p class="text-sm font-semibold">Transfer</p>
            </button>
            <button class="card glassmorphism p-4 rounded-lg text-center hover:scale-105 transition-transform">
                <i class="fas fa-coins text-2xl accent mb-2"></i>
                <p class="text-sm font-semibold">Stacking</p>
            </button>
        </div>

        <!-- Forms Preview -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

            <!-- Sample Form -->
            <div class="card glassmorphism p-6 rounded-lg">
                <h3 class="text-xl font-bold accent mb-4">💰 Quick Deposit</h3>
                <form class="space-y-4">
                    <div>
                        <label class="block text-gray-400 mb-2">Amount ($)</label>
                        <input type="number" placeholder="Enter amount"
                            class="w-full bg-black bg-opacity-20 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-accent">
                    </div>
                    <div>
                        <label class="block text-gray-400 mb-2">Payment Method</label>
                        <select
                            class="w-full bg-black bg-opacity-20 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-accent">
                            <option>Credit Card</option>
                            <option>Bank Transfer</option>
                            <option>Crypto</option>
                        </select>
                    </div>
                    <button type="button"
                        class="w-full accent text-black py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity">
                        Proceed to Deposit
                    </button>
                </form>
            </div>

            <!-- Transaction History -->
            <div class="card glassmorphism p-6 rounded-lg">
                <h3 class="text-xl font-bold secondary mb-4">📊 Recent Transactions</h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-black bg-opacity-20 rounded-lg">
                        <div>
                            <p class="font-semibold">Deposit</p>
                            <p class="text-gray-400 text-sm">2024-06-20</p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold accent">$500.00</p>
                            <span class="px-2 py-1 bg-green-600 text-green-100 text-xs rounded-full">Completed</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-black bg-opacity-20 rounded-lg">
                        <div>
                            <p class="font-semibold">Staking</p>
                            <p class="text-gray-400 text-sm">2024-06-19</p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold secondary">2.5 BTC</p>
                            <span class="px-2 py-1 bg-blue-600 text-blue-100 text-xs rounded-full">Active</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-black bg-opacity-20 rounded-lg">
                        <div>
                            <p class="font-semibold">Transfer</p>
                            <p class="text-gray-400 text-sm">2024-06-18</p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">$1,200.00</p>
                            <span class="px-2 py-1 bg-yellow-600 text-yellow-100 text-xs rounded-full">Pending</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <!-- Bottom Navigation -->
    <nav
        class="fixed bottom-0 left-0 right-0 backdrop-blur-xl bg-black bg-opacity-40 border-t border-white border-opacity-10">
        <div class="container mx-auto px-6 py-3">
            <div class="flex justify-between items-center max-w-md mx-auto">
                <button
                    class="nav-btn group flex flex-col items-center justify-center w-12 h-12 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all duration-300 ease-out">
                    <i
                        class="fas fa-share-alt text-base accent group-hover:scale-110 transition-transform duration-200"></i>
                    <span class="text-xs mt-1 font-medium opacity-80 group-hover:opacity-100">Refer</span>
                </button>
                <button
                    class="nav-btn group flex flex-col items-center justify-center w-12 h-12 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all duration-300 ease-out">
                    <i
                        class="fas fa-plus-circle text-base secondary group-hover:scale-110 transition-transform duration-200"></i>
                    <span class="text-xs mt-1 font-medium opacity-80 group-hover:opacity-100">Deposit</span>
                </button>
                <button
                    class="nav-btn group flex flex-col items-center justify-center w-14 h-14 rounded-2xl accent text-black hover:shadow-lg hover:shadow-yellow-500/20 transition-all duration-300 ease-out transform hover:scale-105">
                    <i class="fas fa-wallet text-lg group-hover:scale-110 transition-transform duration-200"></i>
                    <span class="text-xs mt-1 font-semibold">Wallet</span>
                </button>
                <button
                    class="nav-btn group flex flex-col items-center justify-center w-12 h-12 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all duration-300 ease-out">
                    <i
                        class="fas fa-exchange-alt text-base secondary group-hover:scale-110 transition-transform duration-200"></i>
                    <span class="text-xs mt-1 font-medium opacity-80 group-hover:opacity-100">Transfer</span>
                </button>
                <button
                    class="nav-btn group flex flex-col items-center justify-center w-12 h-12 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all duration-300 ease-out">
                    <i
                        class="fas fa-coins text-base accent group-hover:scale-110 transition-transform duration-200"></i>
                    <span class="text-xs mt-1 font-medium opacity-80 group-hover:opacity-100">Stake</span>
                </button>
            </div>
        </div>
    </nav>

    <script>
        const themeSelect = document.getElementById('themeSelect');
        const themeBody = document.getElementById('themeBody');

        themeSelect.addEventListener('change', function () {
            // Remove all theme classes
            themeBody.className = themeBody.className.replace(/theme-\w+/g, '');
            // Add selected theme
            themeBody.classList.add(this.value);
            themeBody.classList.add('min-h-screen', 'transition-all', 'duration-500');
        });
    </script>
</body>

</html>