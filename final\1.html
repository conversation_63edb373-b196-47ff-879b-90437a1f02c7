<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Dashboard</title>

    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        xintegrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />


    <style>
        /* Define the custom theme and other base styles */
        body {
            font-family: 'Inter', sans-serif;
        }

        .theme-ocean {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            /* Using a softer white for better readability */
        }

        .theme-ocean .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            /* For Safari */
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            /* Consistent rounded corners */
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .theme-ocean .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .theme-ocean .accent {
            color: #3b82f6;
        }

        .theme-ocean .secondary {
            color: #06b6d4;
        }

        /* Marquee animation for scrolling text */
        .marquee {
            white-space: nowrap;
            overflow: hidden;
            box-sizing: border-box;
        }

        .marquee-content {
            display: inline-block;
            padding-left: 100%;
            animation: marquee 15s linear infinite;
        }

        @keyframes marquee {
            0% {
                transform: translateX(0);
            }

            100% {
                transform: translateX(-100%);
            }
        }

        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 5px;
        }

        ::-webkit-scrollbar-track {
            background: #1e293b;
        }

        ::-webkit-scrollbar-thumb {
            background: #3b82f6;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #2563eb;
        }

        /* Enhanced Action Button Styles */
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 0.5rem;
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            background: rgba(30, 41, 59, 0.8);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .action-btn:active {
            transform: translateY(0);
        }

        .action-icon-container {
            width: 3rem;
            height: 3rem;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid;
            transition: all 0.3s ease;
        }

        .action-btn:hover .action-icon-container {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .action-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #e2e8f0;
            text-align: center;
            transition: color 0.3s ease;
        }

        .action-btn:hover .action-label {
            color: #ffffff;
        }

        /* Enhanced Stats Cards */
        .stats-card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1.25rem;
            padding: 1.25rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stats-card:hover::before {
            opacity: 1;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>

<body class="theme-ocean flex justify-center items-center min-h-screen p-4">

    <!-- Mobile phone container -->
    <div
        class="w-full max-w-sm h-[850px] bg-slate-900 rounded-[40px] shadow-2xl overflow-hidden border-4 border-gray-800 flex flex-col">

        <!-- Phone Notch -->
        <div class="flex-shrink-0 px-6 pt-4">
            <div class="relative h-7 bg-slate-900">
                <div class="absolute top-0 left-1/2 -translate-x-1/2 w-24 h-5 bg-gray-800 rounded-b-xl"></div>
            </div>
        </div>

        <!-- Top Navigation Bar -->
        <header class="flex-shrink-0 flex items-center justify-between px-6 py-3 border-b border-slate-700/50">
            <!-- Company Logo -->
            <div class="flex items-center gap-2">
                <i class="fab fa-bitcoin fa-2x secondary"></i>
                <span class="font-bold text-white text-lg">CryptoApp</span>
            </div>
            <!-- User Avatar -->
            <div
                class="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center border-2 border-blue-400">
                <i class="fas fa-user fa-lg accent"></i>
            </div>
        </header>


        <!-- Main content with scrolling -->
        <div class="flex-grow overflow-y-auto px-6 py-4">

            <!-- Offer Message -->
            <div class="card marquee text-center mb-4">
                <p class="marquee-content accent font-semibold">
                    LIMITED TIME OFFER: Get 20% Bonus on First Deposit! 🎉
                </p>
            </div>

            <!-- Enhanced Stats Grid -->
            <div class="grid grid-cols-2 gap-3 mb-6">
                <div class="stats-card text-center">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-wallet text-blue-400 mr-2"></i>
                        <p class="text-xs text-slate-400 font-medium">Standard Account</p>
                    </div>
                    <p class="text-2xl font-bold text-white">$100.00</p>
                    <div class="w-full bg-slate-700 rounded-full h-1.5 mt-2">
                        <div class="bg-blue-400 h-1.5 rounded-full" style="width: 75%"></div>
                    </div>
                </div>
                <div class="stats-card text-center">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-chart-line text-green-400 mr-2"></i>
                        <p class="text-xs text-slate-400 font-medium">Today's Profit</p>
                    </div>
                    <p class="text-2xl font-bold text-green-400">$0.75</p>
                    <p class="text-xs text-green-300 mt-1">+0.75% ↗</p>
                </div>
                <div class="stats-card text-center">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-trophy text-yellow-400 mr-2"></i>
                        <p class="text-xs text-slate-400 font-medium">Total Profit</p>
                    </div>
                    <p class="text-2xl font-bold text-yellow-400">$110.75</p>
                    <p class="text-xs text-yellow-300 mt-1">+10.75% ↗</p>
                </div>
                <div class="stats-card text-center">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-coins text-purple-400 mr-2"></i>
                        <p class="text-xs text-slate-400 font-medium">Wallet Balance</p>
                    </div>
                    <p class="text-2xl font-bold text-white">$0.00</p>
                    <p class="text-xs text-slate-400 mt-1">Available</p>
                </div>
            </div>

            <!-- Enhanced Stacking Info -->
            <div class="stats-card mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-2">
                        <i class="fas fa-layer-group text-cyan-400"></i>
                        <h3 class="font-bold text-white">Active Staking</h3>
                    </div>
                    <div class="flex items-center gap-1 bg-green-500/20 px-2 py-1 rounded-full">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-xs text-green-400 font-medium">Live</span>
                    </div>
                </div>

                <div class="grid grid-cols-3 gap-4 mb-4">
                    <div class="text-center">
                        <p class="text-xs text-slate-400 mb-1">Staked Amount</p>
                        <p class="text-lg font-bold text-white">$50.00</p>
                    </div>
                    <div class="text-center">
                        <div class="relative mx-auto w-12 h-12 mb-2">
                            <img src="https://img.icons8.com/fluency/48/bitcoin.png" alt="BTC" class="w-full h-full" />
                            <div
                                class="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-fire text-xs text-white"></i>
                            </div>
                        </div>
                        <p class="text-xs text-slate-400">1 BTC = $0.45</p>
                    </div>
                    <div class="text-center">
                        <p class="text-xs text-slate-400 mb-1">Current Value</p>
                        <p class="text-lg font-bold text-cyan-400">$52.00</p>
                        <p class="text-xs text-green-400">+4.0% ↗</p>
                    </div>
                </div>

                <div class="bg-slate-800/50 rounded-lg p-3 mb-3">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-xs text-slate-400">Staking Progress</span>
                        <span class="text-xs text-cyan-400 font-medium">65%</span>
                    </div>
                    <div class="w-full bg-slate-700 rounded-full h-2">
                        <div class="bg-gradient-to-r from-cyan-400 to-blue-500 h-2 rounded-full transition-all duration-1000"
                            style="width: 65%"></div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4 text-xs text-slate-400">
                    <div>
                        <p class="mb-1">Registration:</p>
                        <p class="text-white font-medium">01/01/24 10:30:00</p>
                    </div>
                    <div>
                        <p class="mb-1">Last Top-up:</p>
                        <p class="text-white font-medium">15/06/24 18:45:15</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons - Enhanced Design -->
            <div class="grid grid-cols-4 gap-3 mb-6">
                <button class="action-btn group" data-action="deposit">
                    <div class="action-icon-container bg-blue-500/20 border-blue-400/30">
                        <i
                            class="fas fa-download text-blue-400 group-hover:scale-110 transition-transform duration-200"></i>
                    </div>
                    <span class="action-label">Deposit</span>
                </button>
                <button class="action-btn group" data-action="topup">
                    <div class="action-icon-container bg-green-500/20 border-green-400/30">
                        <i
                            class="fas fa-plus text-green-400 group-hover:scale-110 transition-transform duration-200"></i>
                    </div>
                    <span class="action-label">Topup</span>
                </button>
                <button class="action-btn group" data-action="transfer">
                    <div class="action-icon-container bg-purple-500/20 border-purple-400/30">
                        <i
                            class="fas fa-exchange-alt text-purple-400 group-hover:scale-110 transition-transform duration-200"></i>
                    </div>
                    <span class="action-label">Transfer</span>
                </button>
                <button class="action-btn group" data-action="stack">
                    <div class="action-icon-container bg-cyan-500/20 border-cyan-400/30">
                        <i
                            class="fas fa-layer-group text-cyan-400 group-hover:scale-110 transition-transform duration-200"></i>
                    </div>
                    <span class="action-label">Stack</span>
                </button>
            </div>

            <!-- Recent Transactions -->
            <div class="card">
                <h3 class="font-bold text-white mb-3">Recent Transactions</h3>
                <ul class="space-y-3">
                    <li class="flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                                <i class="fas fa-arrow-down text-green-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Deposit from Binance</p>
                                <p class="text-xs text-slate-400">July 03, 2025</p>
                            </div>
                        </div>
                        <p class="font-semibold text-green-400">+$50.00</p>
                    </li>
                    <li class="flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center">
                                <i class="fas fa-arrow-up text-red-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Withdrawal to Bank</p>
                                <p class="text-xs text-slate-400">July 01, 2025</p>
                            </div>
                        </div>
                        <p class="font-semibold text-red-400">-$25.50</p>
                    </li>
                    <li class="flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-sky-500/20 flex items-center justify-center">
                                <i class="fas fa-sync-alt text-sky-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Staking Reward</p>
                                <p class="text-xs text-slate-400">June 30, 2025</p>
                            </div>
                        </div>
                        <p class="font-semibold text-sky-400">+$2.00</p>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="flex-shrink-0">
            <div class="bg-slate-800/80 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="#" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="#" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="#" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="#" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Enhanced Interactions -->
    <script>
        // Action button click handlers
        document.querySelectorAll('.action-btn').forEach(button => {
            button.addEventListener('click', function () {
                const action = this.dataset.action;

                // Add click animation
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // Handle different actions
                switch (action) {
                    case 'deposit':
                        showActionModal('Deposit', 'Add funds to your account', 'fas fa-download', '#3b82f6');
                        break;
                    case 'topup':
                        showActionModal('Top-up', 'Increase your balance', 'fas fa-plus', '#22c55e');
                        break;
                    case 'transfer':
                        showActionModal('Transfer', 'Send funds to another account', 'fas fa-exchange-alt', '#a855f7');
                        break;
                    case 'stack':
                        showActionModal('Stack', 'Start staking your crypto', 'fas fa-layer-group', '#06b6d4');
                        break;
                }
            });
        });

        // Modal functionality
        function showActionModal(title, description, icon, color) {
            // Create modal overlay
            const overlay = document.createElement('div');
            overlay.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4';

            // Create modal content
            overlay.innerHTML = `
                <div class="bg-slate-800 rounded-2xl p-6 w-full max-w-sm border border-slate-700 transform scale-95 opacity-0 transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style="background: ${color}20; border: 2px solid ${color}30;">
                            <i class="${icon} text-2xl" style="color: ${color};"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-2">${title}</h3>
                        <p class="text-slate-400 text-sm">${description}</p>
                    </div>

                    <div class="space-y-4 mb-6">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">Amount</label>
                            <input type="number" placeholder="0.00" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">Currency</label>
                            <select class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-blue-400 focus:outline-none">
                                <option>USD</option>
                                <option>BTC</option>
                                <option>ETH</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex gap-3">
                        <button class="flex-1 bg-slate-700 text-white py-3 rounded-lg font-medium hover:bg-slate-600 transition-colors" onclick="closeModal()">
                            Cancel
                        </button>
                        <button class="flex-1 text-white py-3 rounded-lg font-medium transition-colors" style="background: ${color};" onclick="processAction('${action}')">
                            Confirm
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(overlay);

            // Animate modal in
            setTimeout(() => {
                const modal = overlay.querySelector('div');
                modal.style.transform = 'scale(1)';
                modal.style.opacity = '1';
            }, 10);

            // Close on overlay click
            overlay.addEventListener('click', function (e) {
                if (e.target === overlay) {
                    closeModal();
                }
            });
        }

        function closeModal() {
            const overlay = document.querySelector('.fixed.inset-0');
            if (overlay) {
                const modal = overlay.querySelector('div');
                modal.style.transform = 'scale(0.95)';
                modal.style.opacity = '0';
                setTimeout(() => {
                    overlay.remove();
                }, 300);
            }
        }

        function processAction(action) {
            // Simulate processing
            const button = document.querySelector(`[data-action="${action}"]`);
            const originalContent = button.innerHTML;

            button.innerHTML = `
                <div class="action-icon-container bg-gray-500/20 border-gray-400/30">
                    <i class="fas fa-spinner fa-spin text-gray-400"></i>
                </div>
                <span class="action-label">Processing...</span>
            `;

            setTimeout(() => {
                button.innerHTML = originalContent;
                closeModal();

                // Show success message
                showToast(`${action.charAt(0).toUpperCase() + action.slice(1)} completed successfully!`, 'success');
            }, 2000);
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg text-white font-medium transform translate-x-full transition-transform duration-300 ${type === 'success' ? 'bg-green-500' : 'bg-blue-500'
                }`;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            setTimeout(() => {
                toast.style.transform = 'translateX(full)';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // Animate stats on load
        window.addEventListener('load', function () {
            const progressBars = document.querySelectorAll('[style*="width:"]');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>

</body>

</html>