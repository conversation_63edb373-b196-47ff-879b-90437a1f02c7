<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Dashboard</title>

    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        xintegrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />


    <style>
        /* Define the custom theme and other base styles */
        body {
            font-family: 'Inter', sans-serif;
        }

        .theme-ocean {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            /* Using a softer white for better readability */
        }

        .theme-ocean .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            /* For Safari */
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            /* Consistent rounded corners */
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .theme-ocean .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .theme-ocean .accent {
            color: #3b82f6;
        }

        .theme-ocean .secondary {
            color: #06b6d4;
        }

        /* Marquee animation for scrolling text */
        .marquee {
            white-space: nowrap;
            overflow: hidden;
            box-sizing: border-box;
        }

        .marquee-content {
            display: inline-block;
            padding-left: 100%;
            animation: marquee 15s linear infinite;
        }

        @keyframes marquee {
            0% {
                transform: translateX(0);
            }

            100% {
                transform: translateX(-100%);
            }
        }

        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 5px;
        }

        ::-webkit-scrollbar-track {
            background: #1e293b;
        }

        ::-webkit-scrollbar-thumb {
            background: #3b82f6;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #2563eb;
        }

        /* Action Button Styles - Matching Reference Image */
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 0.5rem;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-2px);
        }

        .action-btn:active {
            transform: translateY(0);
        }

        .action-icon-circle {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-icon-circle::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .action-btn:hover .action-icon-circle {
            transform: scale(1.1);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .action-btn:hover .action-icon-circle::before {
            opacity: 1;
        }

        .action-icon-circle i {
            font-size: 1.25rem;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .action-btn:hover .action-icon-circle i {
            transform: scale(1.1);
        }

        .action-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #ffffff;
            text-align: center;
            transition: all 0.3s ease;
        }

        .action-btn:hover .action-label {
            color: #3b82f6;
        }

        /* Enhanced Stats Cards */
        .stats-card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1.25rem;
            padding: 1.25rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stats-card:hover::before {
            opacity: 1;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        /* Enhanced Stats Card Styles */
        .stats-card-interactive {
            background: rgba(30, 41, 59, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1.5rem;
            padding: 1.5rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            min-height: 140px;
        }

        .stats-card-interactive::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--card-accent), transparent);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .stats-card-interactive:hover::before {
            opacity: 1;
        }

        .stats-card-interactive::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, var(--card-accent-alpha) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
        }

        .stats-card-interactive:hover::after {
            opacity: 0.1;
        }

        .stats-card-interactive:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: var(--card-accent);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5), 0 0 30px var(--card-accent-alpha);
        }

        .stats-card-interactive:active {
            transform: translateY(-4px) scale(1.01);
        }

        /* Card Icon Styles */
        .card-icon-container {
            width: 3rem;
            height: 3rem;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--card-accent-alpha);
            border: 1px solid var(--card-accent);
            transition: all 0.4s ease;
            margin: 0 auto 1rem;
        }

        .stats-card-interactive:hover .card-icon-container {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 20px var(--card-accent-alpha);
        }

        .card-icon {
            font-size: 1.25rem;
            color: var(--card-accent);
            transition: all 0.4s ease;
        }

        .stats-card-interactive:hover .card-icon {
            transform: scale(1.1);
            filter: brightness(1.2);
        }

        /* Card Value Animation */
        .card-value {
            font-size: 1.75rem;
            font-weight: 700;
            color: #ffffff;
            transition: all 0.4s ease;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .stats-card-interactive:hover .card-value {
            transform: scale(1.05);
            color: var(--card-accent);
            text-shadow: 0 0 10px var(--card-accent-alpha);
        }

        /* Progress Bar Enhancement */
        .enhanced-progress {
            width: 100%;
            height: 4px;
            background: rgba(30, 41, 59, 0.8);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 0.75rem;
            position: relative;
        }

        .enhanced-progress::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(90deg, var(--card-accent), var(--card-accent-light));
            border-radius: 2px;
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 0 10px var(--card-accent-alpha);
        }

        /* Card Specific Colors */
        .card-blue {
            --card-accent: #3b82f6;
            --card-accent-light: #60a5fa;
            --card-accent-alpha: rgba(59, 130, 246, 0.3);
        }

        .card-green {
            --card-accent: #10b981;
            --card-accent-light: #34d399;
            --card-accent-alpha: rgba(16, 185, 129, 0.3);
        }

        .card-yellow {
            --card-accent: #f59e0b;
            --card-accent-light: #fbbf24;
            --card-accent-alpha: rgba(245, 158, 11, 0.3);
        }

        .card-purple {
            --card-accent: #8b5cf6;
            --card-accent-light: #a78bfa;
            --card-accent-alpha: rgba(139, 92, 246, 0.3);
        }

        /* Pulse Animation for Live Data */
        .live-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
            margin-right: 0.5rem;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
                transform: scale(1);
            }

            50% {
                opacity: 0.5;
                transform: scale(1.2);
            }

            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Card Subtitle Enhancement */
        .card-subtitle {
            font-size: 0.75rem;
            color: #94a3b8;
            font-weight: 500;
            transition: color 0.4s ease;
        }

        .stats-card-interactive:hover .card-subtitle {
            color: #e2e8f0;
        }

        /* Scrollable Transaction Container */
        .transaction-scroll-container {
            max-height: 240px;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 4px;
            margin-right: -4px;
        }

        .transaction-scroll-container::-webkit-scrollbar {
            width: 4px;
        }

        .transaction-scroll-container::-webkit-scrollbar-track {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 2px;
        }

        .transaction-scroll-container::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.6);
            border-radius: 2px;
            transition: background 0.3s ease;
        }

        .transaction-scroll-container::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.8);
        }

        /* Transaction Item Styles */
        .transaction-item {
            padding: 0.75rem;
            border-radius: 0.75rem;
            background: rgba(30, 41, 59, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 0.75rem;
        }

        .transaction-item:hover {
            background: rgba(30, 41, 59, 0.5);
            border-color: rgba(59, 130, 246, 0.2);
            transform: translateX(4px);
        }

        .transaction-item:last-child {
            margin-bottom: 0;
        }

        /* Fade effect for scrollable area */
        .transaction-scroll-container::before {
            content: '';
            position: sticky;
            top: 0;
            display: block;
            height: 8px;
            background: linear-gradient(to bottom, rgba(30, 41, 59, 0.8), transparent);
            z-index: 1;
            margin-bottom: -8px;
        }

        .transaction-scroll-container::after {
            content: '';
            position: sticky;
            bottom: 0;
            display: block;
            height: 8px;
            background: linear-gradient(to top, rgba(30, 41, 59, 0.8), transparent);
            z-index: 1;
            margin-top: -8px;
        }

        /* Crypto Ticker Styles */
        .ticker-container {
            white-space: nowrap;
            width: max-content;
        }

        .animate-scroll {
            animation: scroll 30s linear infinite;
        }

        @keyframes scroll {
            0% {
                transform: translateX(0);
            }

            100% {
                transform: translateX(-50%);
            }
        }

        .ticker-item {
            flex-shrink: 0;
            min-width: max-content;
        }
    </style>
</head>

<body class="theme-ocean min-h-screen bg-slate-900">

    <!-- Main Dashboard Container -->
    <div class="container mx-auto max-w-md min-h-screen bg-slate-900 flex flex-col">

        <!-- Sleek Top Navigation Bar -->
        <header
            class="sticky top-0 z-50 flex-shrink-0 flex items-center justify-between px-4 py-3 border-b border-slate-700/50 bg-slate-900/95 backdrop-blur-sm">
            <!-- Company Logo -->
            <div class="flex items-center gap-2">
                <i class="fab fa-bitcoin fa-lg secondary"></i>
                <span class="font-bold text-white text-lg">CryptoApp</span>
            </div>
            <!-- User Avatar -->
            <div class="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center border border-blue-400">
                <i class="fas fa-user text-sm accent"></i>
            </div>
        </header>

        <!-- Company Highlight Section -->
        <div
            class="flex-shrink-0 px-4 py-4 bg-gradient-to-r from-blue-900/30 via-purple-900/20 to-blue-900/30 border-b border-slate-700/30">
            <div class="bg-slate-800/40 backdrop-blur-sm rounded-2xl p-4 border border-slate-700/50">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center gap-3">
                        <div
                            class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                            <i class="fab fa-bitcoin text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold text-lg">CryptoVault Pro</h3>
                            <p class="text-slate-300 text-sm">Premium Investment Platform</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="flex items-center gap-1 text-green-400 text-sm font-semibold">
                            <i class="fas fa-arrow-up text-xs"></i>
                            <span>+24.5%</span>
                        </div>
                        <p class="text-slate-400 text-xs">This Month</p>
                    </div>
                </div>

                <div class="grid grid-cols-3 gap-3">
                    <div class="text-center">
                        <div class="text-blue-400 font-bold text-lg">15K+</div>
                        <div class="text-slate-400 text-xs">Active Users</div>
                    </div>
                    <div class="text-center border-x border-slate-700/50">
                        <div class="text-purple-400 font-bold text-lg">$2.8M</div>
                        <div class="text-slate-400 text-xs">Total Volume</div>
                    </div>
                    <div class="text-center">
                        <div class="text-green-400 font-bold text-lg">99.9%</div>
                        <div class="text-slate-400 text-xs">Uptime</div>
                    </div>
                </div>

                <div class="mt-4 flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-green-400 text-sm font-medium">Live Trading</span>
                    </div>
                    <div class="flex items-center gap-1 text-slate-300 text-sm">
                        <i class="fas fa-shield-alt text-blue-400"></i>
                        <span>Secured Platform</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content with scrolling -->
        <div class="flex-grow overflow-y-auto px-6 py-6">

            <!-- Offer Message -->
            <div class="card marquee text-center mb-4">
                <p class="marquee-content accent font-semibold">
                    LIMITED TIME OFFER: Get 20% Bonus on First Deposit! 🎉
                </p>
            </div>

            <!-- Enhanced Interactive Stats Grid -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <!-- Standard Account Card -->
                <div class="stats-card-interactive card-blue text-center" data-card="account"
                    onclick="showCardDetails('account')">
                    <div class="card-icon-container">
                        <i class="fas fa-wallet card-icon"></i>
                    </div>
                    <p class="card-subtitle mb-2">
                        <span class="live-indicator"></span>Standard Account
                    </p>
                    <p class="card-value mb-2" data-value="100.00">$100.00</p>
                    <div class="enhanced-progress" data-progress="75"></div>
                    <p class="text-xs text-slate-400 mt-2">75% Active</p>
                </div>

                <!-- Today's Profit Card -->
                <div class="stats-card-interactive card-green text-center" data-card="today"
                    onclick="showCardDetails('today')">
                    <div class="card-icon-container">
                        <i class="fas fa-chart-line card-icon"></i>
                    </div>
                    <p class="card-subtitle mb-2">
                        <span class="live-indicator"></span>Today's Profit
                    </p>
                    <p class="card-value mb-1" data-value="12.75">$12.75</p>
                    <div class="flex items-center justify-center gap-2">
                        <span class="text-xs text-green-300">+2.55% ↗</span>
                        <span class="text-xs text-slate-400">vs yesterday</span>
                    </div>
                </div>

                <!-- Total Profit Card -->
                <div class="stats-card-interactive card-yellow text-center" data-card="total"
                    onclick="showCardDetails('total')">
                    <div class="card-icon-container">
                        <i class="fas fa-trophy card-icon"></i>
                    </div>
                    <p class="card-subtitle mb-2">
                        <span class="live-indicator"></span>Total Profit
                    </p>
                    <p class="card-value mb-1" data-value="245.50">$245.50</p>
                    <div class="flex items-center justify-center gap-2">
                        <span class="text-xs text-yellow-300">+24.55% ↗</span>
                        <span class="text-xs text-slate-400">all time</span>
                    </div>
                </div>

                <!-- Wallet Balance Card -->
                <div class="stats-card-interactive card-purple text-center" data-card="wallet"
                    onclick="showCardDetails('wallet')">
                    <div class="card-icon-container">
                        <i class="fas fa-coins card-icon"></i>
                    </div>
                    <p class="card-subtitle mb-2">
                        <span class="live-indicator"></span>Wallet Balance
                    </p>
                    <p class="card-value mb-1" data-value="85.30">$85.30</p>
                    <div class="flex items-center justify-center gap-2">
                        <span class="text-xs text-purple-300">Available</span>
                        <span class="text-xs text-slate-400">• Ready to use</span>
                    </div>
                </div>
            </div>

            <!-- Enhanced Stacking Info -->
            <div class="stats-card mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-2">
                        <i class="fas fa-layer-group text-cyan-400"></i>
                        <h3 class="font-bold text-white">Active Staking</h3>
                    </div>
                    <div class="flex items-center gap-1 bg-green-500/20 px-2 py-1 rounded-full">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-xs text-green-400 font-medium">Live</span>
                    </div>
                </div>

                <div class="grid grid-cols-3 gap-4 mb-4">
                    <div class="text-center">
                        <p class="text-xs text-slate-400 mb-1">Staked Amount</p>
                        <p class="text-lg font-bold text-white">$50.00</p>
                    </div>
                    <div class="text-center">
                        <div class="relative mx-auto w-12 h-12 mb-2">
                            <img src="https://img.icons8.com/fluency/48/bitcoin.png" alt="BTC" class="w-full h-full" />
                            <div
                                class="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-fire text-xs text-white"></i>
                            </div>
                        </div>
                        <p class="text-xs text-slate-400">1 BTC = $0.45</p>
                    </div>
                    <div class="text-center">
                        <p class="text-xs text-slate-400 mb-1">Current Value</p>
                        <p class="text-lg font-bold text-cyan-400">$52.00</p>
                        <p class="text-xs text-green-400">+4.0% ↗</p>
                    </div>
                </div>

                <div class="bg-slate-800/50 rounded-lg p-3 mb-3">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-xs text-slate-400">Staking Progress</span>
                        <span class="text-xs text-cyan-400 font-medium">65%</span>
                    </div>
                    <div class="w-full bg-slate-700 rounded-full h-2">
                        <div class="bg-gradient-to-r from-cyan-400 to-blue-500 h-2 rounded-full transition-all duration-1000"
                            style="width: 65%"></div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4 text-xs text-slate-400">
                    <div>
                        <p class="mb-1">Registration:</p>
                        <p class="text-white font-medium">01/01/24 10:30:00</p>
                    </div>
                    <div>
                        <p class="mb-1">Last Top-up:</p>
                        <p class="text-white font-medium">15/06/24 18:45:15</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons - Circular Design -->
            <div class="grid grid-cols-4 gap-4 mb-6">
                <button class="action-btn" data-action="deposit">
                    <div class="action-icon-circle">
                        <i class="fas fa-download text-blue-400"></i>
                    </div>
                    <span class="action-label">Deposit</span>
                </button>
                <button class="action-btn" data-action="topup">
                    <div class="action-icon-circle">
                        <i class="fas fa-plus text-blue-400"></i>
                    </div>
                    <span class="action-label">Topup</span>
                </button>
                <button class="action-btn" data-action="transfer">
                    <div class="action-icon-circle">
                        <i class="fas fa-exchange-alt text-blue-400"></i>
                    </div>
                    <span class="action-label">Transfer</span>
                </button>
                <button class="action-btn" data-action="stack">
                    <div class="action-icon-circle">
                        <i class="fas fa-layer-group text-blue-400"></i>
                    </div>
                    <span class="action-label">Stack</span>
                </button>
            </div>

            <!-- Recent Transactions - Scrollable -->
            <div class="stats-card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-bold text-white">Recent Transactions</h3>
                    <div class="text-xs text-slate-400 bg-slate-700/50 px-2 py-1 rounded-full">
                        Scroll for more
                    </div>
                </div>

                <!-- Scrollable Transaction List -->
                <div class="transaction-scroll-container">
                    <ul class="space-y-3">
                        <!-- Network Profit Transaction -->
                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                    <i class="fas fa-network-wired text-green-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Network Profit</p>
                                    <p class="text-xs text-slate-400">July 04, 2025 - 14:30</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-400">+$18.50</p>
                                <p class="text-xs text-green-300">+3.7%</p>
                            </div>
                        </li>

                        <!-- Withdrawal Transaction -->
                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center border border-red-500/30">
                                    <i class="fas fa-arrow-up text-red-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Withdrawal to Bank</p>
                                    <p class="text-xs text-slate-400">July 03, 2025 - 16:45</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-red-400">-$25.00</p>
                                <p class="text-xs text-slate-400">Completed</p>
                            </div>
                        </li>

                        <!-- Daily Profit Transaction -->
                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                    <i class="fas fa-calendar-day text-green-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Daily Profit</p>
                                    <p class="text-xs text-slate-400">July 02, 2025 - 09:15</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-400">+$12.25</p>
                                <p class="text-xs text-green-300">+2.5%</p>
                            </div>
                        </li>

                        <!-- Additional Scrollable Transactions -->
                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center border border-red-500/30">
                                    <i class="fas fa-credit-card text-red-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Withdrawal to Wallet</p>
                                    <p class="text-xs text-slate-400">July 01, 2025 - 11:20</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-red-400">-$15.30</p>
                                <p class="text-xs text-slate-400">Pending</p>
                            </div>
                        </li>

                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                    <i class="fas fa-trophy text-green-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Bonus Profit</p>
                                    <p class="text-xs text-slate-400">June 30, 2025 - 18:00</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-400">+$20.00</p>
                                <p class="text-xs text-green-300">+4.0%</p>
                            </div>
                        </li>

                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center border border-red-500/30">
                                    <i class="fas fa-university text-red-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Bank Withdrawal</p>
                                    <p class="text-xs text-slate-400">June 29, 2025 - 14:30</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-red-400">-$50.00</p>
                                <p class="text-xs text-slate-400">Completed</p>
                            </div>
                        </li>

                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                    <i class="fas fa-sun text-green-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Daily Profit</p>
                                    <p class="text-xs text-slate-400">June 28, 2025 - 10:45</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-400">+$15.75</p>
                                <p class="text-xs text-green-300">+3.2%</p>
                            </div>
                        </li>

                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                    <i class="fas fa-globe text-green-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Network Profit</p>
                                    <p class="text-xs text-slate-400">June 27, 2025 - 16:20</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-400">+$22.40</p>
                                <p class="text-xs text-green-300">+4.5%</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="#" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="#" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="#" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="#" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- JavaScript for Enhanced Interactions -->
        <script>
            // Action button click handlers
            document.querySelectorAll('.action-btn').forEach(button => {
                button.addEventListener('click', function () {
                    const action = this.dataset.action;

                    // Add click animation
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);

                    // Handle different actions
                    switch (action) {
                        case 'deposit':
                            showActionModal('Deposit', 'Add funds to your account', 'fas fa-download', '#3b82f6');
                            break;
                        case 'topup':
                            showActionModal('Top-up', 'Increase your balance', 'fas fa-plus', '#22c55e');
                            break;
                        case 'transfer':
                            showActionModal('Transfer', 'Send funds to another account', 'fas fa-exchange-alt', '#a855f7');
                            break;
                        case 'stack':
                            showActionModal('Stack', 'Start staking your crypto', 'fas fa-layer-group', '#06b6d4');
                            break;
                    }
                });
            });

            // Modal functionality
            function showActionModal(title, description, icon, color) {
                // Create modal overlay
                const overlay = document.createElement('div');
                overlay.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4';

                // Create modal content
                overlay.innerHTML = `
                <div class="bg-slate-800 rounded-2xl p-6 w-full max-w-sm border border-slate-700 transform scale-95 opacity-0 transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style="background: ${color}20; border: 2px solid ${color}30;">
                            <i class="${icon} text-2xl" style="color: ${color};"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-2">${title}</h3>
                        <p class="text-slate-400 text-sm">${description}</p>
                    </div>

                    <div class="space-y-4 mb-6">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">Amount</label>
                            <input type="number" placeholder="0.00" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">Currency</label>
                            <select class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-blue-400 focus:outline-none">
                                <option>USD</option>
                                <option>BTC</option>
                                <option>ETH</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex gap-3">
                        <button class="flex-1 bg-slate-700 text-white py-3 rounded-lg font-medium hover:bg-slate-600 transition-colors" onclick="closeModal()">
                            Cancel
                        </button>
                        <button class="flex-1 text-white py-3 rounded-lg font-medium transition-colors" style="background: ${color};" onclick="processAction('${action}')">
                            Confirm
                        </button>
                    </div>
                </div>
            `;

                document.body.appendChild(overlay);

                // Animate modal in
                setTimeout(() => {
                    const modal = overlay.querySelector('div');
                    modal.style.transform = 'scale(1)';
                    modal.style.opacity = '1';
                }, 10);

                // Close on overlay click
                overlay.addEventListener('click', function (e) {
                    if (e.target === overlay) {
                        closeModal();
                    }
                });
            }

            function closeModal() {
                const overlay = document.querySelector('.fixed.inset-0');
                if (overlay) {
                    const modal = overlay.querySelector('div');
                    modal.style.transform = 'scale(0.95)';
                    modal.style.opacity = '0';
                    setTimeout(() => {
                        overlay.remove();
                    }, 300);
                }
            }

            function processAction(action) {
                // Simulate processing
                const button = document.querySelector(`[data-action="${action}"]`);
                const originalContent = button.innerHTML;

                button.innerHTML = `
                <div class="action-icon-container bg-gray-500/20 border-gray-400/30">
                    <i class="fas fa-spinner fa-spin text-gray-400"></i>
                </div>
                <span class="action-label">Processing...</span>
            `;

                setTimeout(() => {
                    button.innerHTML = originalContent;
                    closeModal();

                    // Show success message
                    showToast(`${action.charAt(0).toUpperCase() + action.slice(1)} completed successfully!`, 'success');
                }, 2000);
            }

            function showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg text-white font-medium transform translate-x-full transition-transform duration-300 ${type === 'success' ? 'bg-green-500' : 'bg-blue-500'
                    }`;
                toast.textContent = message;

                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.transform = 'translateX(0)';
                }, 10);

                setTimeout(() => {
                    toast.style.transform = 'translateX(full)';
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }

            // Animate stats on load
            window.addEventListener('load', function () {
                const progressBars = document.querySelectorAll('[style*="width:"]');
                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 500);
                });

                // Initialize enhanced card features
                initializeEnhancedCards();
            });

            // Initialize enhanced card features
            function initializeEnhancedCards() {
                // Initialize enhanced progress bars
                initializeProgressBars();

                // Initialize card animations
                initializeCardAnimations();
            }

            // Initialize enhanced progress bars
            function initializeProgressBars() {
                const progressBars = document.querySelectorAll('.enhanced-progress');
                progressBars.forEach(bar => {
                    const progress = bar.dataset.progress || 0;

                    // Create progress element if it doesn't exist
                    if (!bar.querySelector('.progress-fill')) {
                        const progressFill = document.createElement('div');
                        progressFill.className = 'progress-fill';
                        progressFill.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 0;
                        height: 100%;
                        background: linear-gradient(90deg, var(--card-accent), var(--card-accent-light));
                        border-radius: 2px;
                        transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
                        box-shadow: 0 0 10px var(--card-accent-alpha);
                        width: 0%;
                    `;
                        bar.appendChild(progressFill);
                    }

                    // Animate progress bar
                    setTimeout(() => {
                        const progressFill = bar.querySelector('.progress-fill');
                        if (progressFill) {
                            progressFill.style.width = progress + '%';
                        }
                    }, 1200);
                });
            }

            // Initialize card animations and interactions
            function initializeCardAnimations() {
                const cards = document.querySelectorAll('.stats-card-interactive');

                cards.forEach((card, index) => {
                    // Stagger card entrance animations
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 150);

                    // Add value counting animation
                    const valueElement = card.querySelector('.card-value');
                    if (valueElement) {
                        const targetValue = parseFloat(valueElement.dataset.value);
                        setTimeout(() => {
                            animateValue(valueElement, 0, targetValue, 1500);
                        }, index * 150 + 300);
                    }
                });
            }

            // Animate number counting
            function animateValue(element, start, end, duration) {
                const startTime = performance.now();
                const prefix = element.textContent.includes('$') ? '$' : '';

                function updateValue(currentTime) {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // Easing function for smooth animation
                    const easeOutCubic = 1 - Math.pow(1 - progress, 3);
                    const currentValue = start + (end - start) * easeOutCubic;

                    element.textContent = prefix + currentValue.toFixed(2);

                    if (progress < 1) {
                        requestAnimationFrame(updateValue);
                    }
                }

                requestAnimationFrame(updateValue);
            }

            // Show detailed card information
            function showCardDetails(cardType) {
                const cardData = {
                    account: {
                        title: 'Standard Account Details',
                        content: `
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Account Type:</span>
                                <span class="text-white font-semibold">Standard</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Balance:</span>
                                <span class="text-blue-400 font-semibold">$100.00</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Activity Level:</span>
                                <span class="text-green-400 font-semibold">75% Active</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Last Activity:</span>
                                <span class="text-white">2 hours ago</span>
                            </div>
                            <div class="mt-4 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                                <p class="text-blue-400 text-sm">💡 Upgrade to Premium for higher limits and exclusive features!</p>
                            </div>
                        </div>
                    `,
                        icon: 'fas fa-wallet',
                        color: '#3b82f6'
                    },
                    today: {
                        title: "Today's Profit Breakdown",
                        content: `
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Network Profit:</span>
                                <span class="text-green-400 font-semibold">+$8.50</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Daily Profit:</span>
                                <span class="text-green-400 font-semibold">+$2.75</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Bonus Profit:</span>
                                <span class="text-green-400 font-semibold">+$1.50</span>
                            </div>
                            <div class="border-t border-slate-600 pt-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-white font-semibold">Total Today:</span>
                                    <span class="text-green-400 font-bold text-lg">+$12.75</span>
                                </div>
                            </div>
                            <div class="mt-4 p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                                <p class="text-green-400 text-sm">🚀 Great performance! You're up 2.55% from yesterday.</p>
                            </div>
                        </div>
                    `,
                        icon: 'fas fa-chart-line',
                        color: '#10b981'
                    }
                };

                const data = cardData[cardType];
                if (data) {
                    showActionModal(data.title, data.content, data.icon, data.color);
                }
            }
        </script>

</body>

</html>